import React, { useState, useEffect } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import Input from '../common/Input';
import { Save, RotateCcw } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';
import {
  saveLoanRepaymentIllustration,
  validateLoanRepaymentData,
  type LoanRepaymentIllustrationData
} from '../../services/loanRepaymentService';

// Add the type for the table rows
interface LoanRepaymentTableRow {
  age: number;
  policyYear: string;
  calendarYear: number;
  repaymentAmount: number;
}

const LoanRepaymentPage: React.FC = () => {
  const { selectedCustomerData, selectedPolicyData, setActiveTab, addScenario } = useDashboard();

  // Save scenario state
  const [isSaving, setIsSaving] = useState(false);

  // New loan repayment strategy state
  const [modelLoanRepayment, setModelLoanRepayment] = useState(false);
  const [repaymentType, setRepaymentType] = useState('');

  // Option 1: Change to New Loan Repayment amount now
  const [newLoanAmount, setNewLoanAmount] = useState('');
  const [showNewLoanDropdown, setShowNewLoanDropdown] = useState(false);

  // Option 2: Modify the loan repayment age (following FaceAmount pattern)
  const [ageChangeNow, setAgeChangeNow] = useState(false);
  const [ageChangeLater, setAgeChangeLater] = useState(false);

  // Option 3: Lump Sum (One-time premium) now
  const [lumpSumAmount, setLumpSumAmount] = useState('');
  const [showLumpSumContainer, setShowLumpSumContainer] = useState(false);

  // Add at the top, after imports
  const [loanRepaymentByYearData, setLoanRepaymentByYearData] = useState<{
    selectedTypes: { age: boolean; policyYear: boolean; calendarYear: boolean };
    ageRange: { start: number; end: number };
    policyYearRange: { start: number; end: number };
    calendarYearRange: { start: number; end: number };
    isEditing: boolean;
    tableData: LoanRepaymentTableRow[];
  }>(
    {
      selectedTypes: { age: false, policyYear: false, calendarYear: false },
      ageRange: { start: 40, end: 100 },
      policyYearRange: { start: 1, end: 100 },
      calendarYearRange: { start: 2024, end: 2100 },
      isEditing: false,
      tableData: []
    }
  );

  // Helper functions for current age/year
  const getCurrentYear = () => new Date().getFullYear();
  const calculateCurrentAge = () => 40; // Replace with real logic if available
  const calculateCurrentPolicyYear = () => 1; // Replace with real logic if available

  // Generate table data based on selected types and ranges
  const generateLoanRepaymentTableData = () => {
    const { selectedTypes, ageRange, policyYearRange, calendarYearRange } = loanRepaymentByYearData;
    let startYear = 0, endYear = 0;
    
    // Always generate default data for modify-age option
    if (repaymentType === 'modify-age') {
      const currentAge = calculateCurrentAge();
      const currentCalendarYear = getCurrentYear();
      const currentPolicyYear = calculateCurrentPolicyYear();
      const data = [];
      for (let i = 0; i < 8; i++) {
        data.push({
          age: currentAge + i,
          policyYear: `Year ${currentPolicyYear + i}`,
          calendarYear: currentCalendarYear + i,
          repaymentAmount: 0
        });
      }
      return data;
    }
    
    // Default to age-based calculation if no type is selected
    if (selectedTypes.age || (!selectedTypes.age && !selectedTypes.policyYear && !selectedTypes.calendarYear)) {
      const currentAge = calculateCurrentAge();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (ageRange.start - currentAge);
      endYear = currentCalendarYear + (ageRange.end - currentAge);
    } else if (selectedTypes.policyYear) {
      const currentPolicyYear = calculateCurrentPolicyYear();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (policyYearRange.start - currentPolicyYear);
      endYear = currentCalendarYear + (policyYearRange.end - currentPolicyYear);
    } else if (selectedTypes.calendarYear) {
      startYear = calendarYearRange.start;
      endYear = calendarYearRange.end;
    }
    
    if (startYear === 0 || endYear === 0 || startYear > endYear) {
      // Fallback to default data if no valid range
      const currentAge = calculateCurrentAge();
      const currentCalendarYear = getCurrentYear();
      const currentPolicyYear = calculateCurrentPolicyYear();
      const data = [];
      for (let i = 0; i < 8; i++) {
        data.push({
          age: currentAge + i,
          policyYear: `Year ${currentPolicyYear + i}`,
          calendarYear: currentCalendarYear + i,
          repaymentAmount: 0
        });
      }
      return data;
    }
    
    const currentAge = calculateCurrentAge();
    const currentCalendarYear = getCurrentYear();
    const currentPolicyYear = calculateCurrentPolicyYear();
    const data = [];
    const maxEntries = 12;
    const totalYears = endYear - startYear + 1;
    const actualEndYear = totalYears > maxEntries ? startYear + maxEntries - 1 : endYear;
    for (let year = startYear; year <= actualEndYear; year++) {
      data.push({
        age: currentAge + (year - currentCalendarYear),
        policyYear: `Year ${currentPolicyYear + (year - currentCalendarYear)}`,
        calendarYear: year,
        repaymentAmount: 0 // Default value
      });
    }
    return data;
  };

  // Initialize table data on component mount
  useEffect(() => {
    setLoanRepaymentByYearData(prev => ({
      ...prev,
      tableData: generateLoanRepaymentTableData()
    }));
  }, []);

  // Update table data when selections change
  useEffect(() => {
    setLoanRepaymentByYearData(prev => ({
      ...prev,
      tableData: generateLoanRepaymentTableData()
    }));
    // eslint-disable-next-line
  }, [loanRepaymentByYearData.selectedTypes, loanRepaymentByYearData.ageRange, loanRepaymentByYearData.policyYearRange, loanRepaymentByYearData.calendarYearRange, repaymentType]);

  // Reset all scenario state
  const handleResetScenarios = () => {
    setModelLoanRepayment(false);
    setRepaymentType('');
    setNewLoanAmount('');
    setShowNewLoanDropdown(false);
    setAgeChangeNow(false);
    setAgeChangeLater(false);
    setLumpSumAmount('');
    setShowLumpSumContainer(false);
    // Reset table data
    setLoanRepaymentByYearData({
      selectedTypes: { age: false, policyYear: false, calendarYear: false },
      ageRange: { start: 40, end: 100 },
      policyYearRange: { start: 1, end: 100 },
      calendarYearRange: { start: 2024, end: 2100 },
      isEditing: false,
      tableData: []
    });
    alert('All loan repayment scenarios have been reset!');
  };

  // Handle option selection
  const handleOptionSelect = (option: string) => {
    if (repaymentType === option) {
      setRepaymentType(''); // Deselect if already selected
    } else {
      setRepaymentType(option);
    }

    // Reset all option-specific states first
    setShowNewLoanDropdown(false);
    setShowLumpSumContainer(false);

    // Show relevant UI based on selected option
    if (option === 'new-loan-amount') {
      setShowNewLoanDropdown(true);
    } else if (option === 'lump-sum') {
      setShowLumpSumContainer(true);
    } else if (option === 'modify-age') {
      // Auto-select Age checkbox and set default range to make table visible
      setLoanRepaymentByYearData(prev => ({
        ...prev,
        selectedTypes: { age: true, policyYear: false, calendarYear: false },
        ageRange: { start: 40, end: 50 } // Show 10 years by default
      }));
    }
  };

  // Save scenario function
  const saveScenario = async () => {
    if (!selectedCustomerData || !selectedPolicyData) {
      alert('Please select a customer and policy first!');
      return;
    }

    // Validate that at least one option is selected
    if (!repaymentType) {
      alert('Please select a loan repayment option before saving!');
      return;
    }

    setIsSaving(true);
    try {
      // Get policy ID from selected policy data
      const policyId = parseInt(selectedPolicyData.id) || parseInt(selectedCustomerData.customerId);

      if (!policyId) {
        throw new Error('Policy ID not found in selected data');
      }

      // Prepare data for backend API
      const loanRepaymentData: LoanRepaymentIllustrationData = {
        policy_id: policyId,
        repayment_type: repaymentType,
        new_loan_amount: newLoanAmount ? parseFloat(newLoanAmount) : undefined,
        lump_sum_amount: lumpSumAmount ? parseFloat(lumpSumAmount) : undefined,
        schedule_data: repaymentType === 'modify-age' && loanRepaymentByYearData.tableData.length > 0
          ? loanRepaymentByYearData.tableData.map(row => ({
              age: row.age,
              policy_year: parseInt(row.policyYear.replace('Year ', '')),
              calendar_year: row.calendarYear,
              repayment_amount: row.repaymentAmount
            }))
          : undefined,
        age_range: loanRepaymentByYearData.selectedTypes.age
          ? loanRepaymentByYearData.ageRange
          : undefined,
        policy_year_range: loanRepaymentByYearData.selectedTypes.policyYear
          ? loanRepaymentByYearData.policyYearRange
          : undefined,
        calendar_year_range: loanRepaymentByYearData.selectedTypes.calendarYear
          ? loanRepaymentByYearData.calendarYearRange
          : undefined,
        selected_type: loanRepaymentByYearData.selectedTypes.age ? 'age'
          : loanRepaymentByYearData.selectedTypes.policyYear ? 'policyYear'
          : loanRepaymentByYearData.selectedTypes.calendarYear ? 'calendarYear'
          : undefined
      };

      // Validate data before sending
      const validationErrors = validateLoanRepaymentData(loanRepaymentData);
      if (validationErrors.length > 0) {
        alert(`Validation errors:\n${validationErrors.join('\n')}`);
        return;
      }

      // Calculate current age and policy year (you may need to adjust this based on your data)
      const currentAge = calculateCurrentAge();
      const currentPolicyYear = calculateCurrentPolicyYear();

      // Save to backend
      const result = await saveLoanRepaymentIllustration(loanRepaymentData, currentAge, currentPolicyYear);

      if (result.status === 'SUCCESS') {
        alert('Loan Repayment Analysis saved successfully to database!');

        // ✅ Add scenario to current session for immediate display (maintains fresh start)
        try {
          // Helper function to get selected option description
          const getSelectedOptionDescription = () => {
            switch (loanRepaymentData.repayment_type) {
              case 'new-loan-amount': return `New Loan Amount: $${loanRepaymentData.new_loan_amount?.toLocaleString() || '0'}`;
              case 'lump-sum': return `Lump Sum Payment: $${loanRepaymentData.lump_sum_amount?.toLocaleString() || '0'}`;
              case 'interest-only': return 'Interest Only Payment';
              case 'modify-age': return `Modified Repayment Schedule (${loanRepaymentData.schedule_data?.length || 0} entries)`;
              default: return loanRepaymentData.repayment_type || 'Unknown';
            }
          };

          const scenarioData = {
            id: `loan-repayment-${Date.now()}`,
            name: 'LOAN REPAYMENT TYPE',
            policyId: selectedPolicyData?.id?.toString() || '',
            asIsDetails: 'Loan Repayment Analysis',
            category: 'loan-repayment' as const,
            keyPoints: [
              `Type: Loan Repayment`,
              `Question: Loan Repayment Strategy`,
              `Selected Option: ${getSelectedOptionDescription()}`,
            ],
            whatIfOptions: [getSelectedOptionDescription()],
            data: loanRepaymentData,
            createdAt: new Date(),
            updatedAt: new Date()
          };

          await addScenario(scenarioData);
          console.log('✅ Loan Repayment scenario added to current session');
        } catch (error) {
          console.error('❌ Error adding scenario to session:', error);
        }
      } else {
        throw new Error(result.message || 'Failed to save to database');
      }
    } catch (error) {
      console.error('Error saving scenario:', error);
      alert(`Error saving scenario: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Loan Repayment illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <>
          {/* Scenario Description */}
          <Card className="mb-6 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
            <div className="text-black dark:text-blue-100">
              <h3 className="text-lg font-semibold mb-2">Loan Repayment Scenario Overview</h3>
              <p>
                This scenario shows how repaying outstanding policy loans—fully or partially—affects your policy’s future values. It highlights changes in policy debt, cash value recovery, and potential benefits of restoring policy health using the Current Interest Rate.
              </p>
            </div>
          </Card>
          {/* New Loan Repayment Strategy */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">Loan Repayment Strategy (scheduled or lump sum)</h2>
            <div className="space-y-6 pl-6 mt-4">
              {/* Option 1: Change to New Loan Repayment amount now */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    name="repaymentType"
                    value="new-loan-amount"
                    checked={repaymentType === 'new-loan-amount'}
                    onClick={() => handleOptionSelect('new-loan-amount')}
                    readOnly
                    className="mr-2"
                  />
                  Change to New Loan Repayment amount now
                </label>
                {repaymentType === 'new-loan-amount' && (
                  <div className="grid grid-cols-2 gap-4 mt-2 ml-6">
                    <div>
                      <Input
                        value={newLoanAmount}
                        onChange={e => setNewLoanAmount(e.target.value)}
                        placeholder="Enter amount"
                        className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Option 2: Modify the loan repayment age (following FaceAmount pattern) */}
              <div className="space-y-4">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    name="repaymentType"
                    value="modify-age"
                    checked={repaymentType === 'modify-age'}
                    onClick={() => handleOptionSelect('modify-age')}
                    readOnly
                    className="mr-2"
                  />
                  Modify the loan repayment Schedule
                </label>
                {repaymentType === 'modify-age' && (
                  <div className="bg-white p-6 rounded-lg border border-gray-300 mt-4 space-y-6">
                    {/* Type Selection Checkboxes - only one can be selected */}
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <label className="flex items-center text-black font-semibold">
                        <input
                          type="checkbox"
                          checked={loanRepaymentByYearData.selectedTypes.age}
                          onChange={e => setLoanRepaymentByYearData(prev => ({ ...prev, selectedTypes: { age: e.target.checked, policyYear: false, calendarYear: false } }))}
                          className="mr-2"
                        />
                        Age
                      </label>
                      <label className="flex items-center text-black font-semibold">
                        <input
                          type="checkbox"
                          checked={loanRepaymentByYearData.selectedTypes.policyYear}
                          onChange={e => setLoanRepaymentByYearData(prev => ({ ...prev, selectedTypes: { age: false, policyYear: e.target.checked, calendarYear: false } }))}
                          className="mr-2"
                        />
                        Policy Year
                      </label>
                      <label className="flex items-center text-black font-semibold">
                        <input
                          type="checkbox"
                          checked={loanRepaymentByYearData.selectedTypes.calendarYear}
                          onChange={e => setLoanRepaymentByYearData(prev => ({ ...prev, selectedTypes: { age: false, policyYear: false, calendarYear: e.target.checked } }))}
                          className="mr-2"
                        />
                        Calendar Year
                      </label>
                    </div>
                    {/* Range Selectors with increment/decrement buttons */}
                    {loanRepaymentByYearData.selectedTypes.age && (
                      <div className="grid grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                          <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                            <button type="button" onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, ageRange: { ...prev.ageRange, start: Math.max(0, prev.ageRange.start - 1) } }))} className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white">◀</button>
                            <span className="text-xl font-bold text-black min-w-[3rem] text-center">{loanRepaymentByYearData.ageRange.start}</span>
                            <button type="button" onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, ageRange: { ...prev.ageRange, start: Math.min(100, prev.ageRange.start + 1) } }))} className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white">▶</button>
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-bold text-black mb-2">End Age</label>
                          <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                            <button type="button" onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, ageRange: { ...prev.ageRange, end: Math.max(prev.ageRange.start, prev.ageRange.end - 1) } }))} className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white">◀</button>
                            <span className="text-xl font-bold text-black min-w-[3rem] text-center">{loanRepaymentByYearData.ageRange.end}</span>
                            <button type="button" onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, ageRange: { ...prev.ageRange, end: Math.min(100, prev.ageRange.end + 1) } }))} className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white">▶</button>
                          </div>
                        </div>
                      </div>
                    )}
                    {loanRepaymentByYearData.selectedTypes.policyYear && (
                      <div className="grid grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                          <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                            <button type="button" onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, policyYearRange: { ...prev.policyYearRange, start: Math.max(1, prev.policyYearRange.start - 1) } }))} className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white">◀</button>
                            <span className="text-xl font-bold text-black min-w-[3rem] text-center">{loanRepaymentByYearData.policyYearRange.start}</span>
                            <button type="button" onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, policyYearRange: { ...prev.policyYearRange, start: Math.min(100, prev.policyYearRange.start + 1) } }))} className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white">▶</button>
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-bold text-black mb-2">End Policy Year</label>
                          <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                            <button type="button" onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, policyYearRange: { ...prev.policyYearRange, end: Math.max(prev.policyYearRange.start, prev.policyYearRange.end - 1) } }))} className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white">◀</button>
                            <span className="text-xl font-bold text-black min-w-[3rem] text-center">{loanRepaymentByYearData.policyYearRange.end}</span>
                            <button type="button" onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, policyYearRange: { ...prev.policyYearRange, end: Math.min(100, prev.policyYearRange.end + 1) } }))} className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white">▶</button>
                          </div>
                        </div>
                      </div>
                    )}
                    {loanRepaymentByYearData.selectedTypes.calendarYear && (
                      <div className="grid grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                          <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                            <button type="button" onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, calendarYearRange: { ...prev.calendarYearRange, start: Math.max(getCurrentYear(), prev.calendarYearRange.start - 1) } }))} className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white">◀</button>
                            <span className="text-xl font-bold text-black min-w-[3rem] text-center">{loanRepaymentByYearData.calendarYearRange.start}</span>
                            <button type="button" onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, calendarYearRange: { ...prev.calendarYearRange, start: Math.min(2100, prev.calendarYearRange.start + 1) } }))} className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white">▶</button>
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-bold text-black mb-2">End Calendar Year</label>
                          <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                            <button type="button" onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, calendarYearRange: { ...prev.calendarYearRange, end: Math.max(prev.calendarYearRange.start, prev.calendarYearRange.end - 1) } }))} className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white">◀</button>
                            <span className="text-xl font-bold text-black min-w-[3rem] text-center">{loanRepaymentByYearData.calendarYearRange.end}</span>
                            <button type="button" onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, calendarYearRange: { ...prev.calendarYearRange, end: Math.min(2100, prev.calendarYearRange.end + 1) } }))} className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white">▶</button>
                          </div>
                        </div>
                      </div>
                    )}
                    {/* Buttons Row */}
                    <div className="flex justify-between items-center mt-6 mb-4">
                      <button className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-600 transition-colors">View Year by Year Details</button>
                      <button onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, isEditing: !prev.isEditing }))} className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors">{loanRepaymentByYearData.isEditing ? 'Lock Schedule' : 'Modify Schedule'}</button>
                    </div>
                    {/* Data Table */}
                    <div className="mt-4">
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse border border-gray-300 bg-white">
                          <thead>
                            <tr className="bg-gray-100">
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Age</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Policy Year</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Calendar Year</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Repayment Amount</th>
                            </tr>
                          </thead>
                          <tbody>
                            {loanRepaymentByYearData.tableData && loanRepaymentByYearData.tableData.length > 0 ? (
                              loanRepaymentByYearData.tableData.map((row, index) => (
                                <tr key={index} className="hover:bg-gray-50">
                                  <td className="border border-gray-300 px-4 py-2 bg-white text-center font-semibold text-black rounded-md mx-1">{row.age}</td>
                                  <td className="border border-gray-300 px-4 py-2 bg-white text-center font-semibold text-black rounded-md mx-1">{row.policyYear}</td>
                                  <td className="border border-gray-300 px-4 py-2 bg-white text-center font-semibold text-black rounded-md mx-1">{row.calendarYear}</td>
                                  <td className="border border-gray-300 px-4 py-2 bg-white text-center font-semibold text-black rounded-md mx-1">
                                    {row.repaymentAmount}
                                  </td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td colSpan={4} className="border border-gray-300 px-4 py-2 text-center text-gray-500">
                                  Loading table data...
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Option 3: Lump Sum (One-time premium) now */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    name="repaymentType"
                    value="lump-sum"
                    checked={repaymentType === 'lump-sum'}
                    onClick={() => handleOptionSelect('lump-sum')}
                    readOnly
                    className="mr-2"
                  />
                  Lump Sum (One-time premium) now
                </label>
                {repaymentType === 'lump-sum' && (
                  <div className="grid grid-cols-2 gap-4 mt-2 ml-6">
                    <div>
                      <Input
                        value={lumpSumAmount}
                        onChange={e => setLumpSumAmount(e.target.value)}
                        placeholder="Enter lump sum amount"
                        className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Option 4: Interest only Payment */}
              <label className="flex items-center text-lg font-semibold text-black mb-2">
                <input
                  type="radio"
                  name="repaymentType"
                  value="interest-only"
                  checked={repaymentType === 'interest-only'}
                  onClick={() => handleOptionSelect('interest-only')}
                  readOnly
                  className="mr-2"
                />
                Interest only Payment
              </label>
            </div>
          </Card>
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={saveScenario}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
              disabled={isSaving}
            >
              <Save className="w-4 h-4" />
              <span>{isSaving ? 'Saving...' : 'Save Loan Repayment Illustration'}</span>
            </Button>
            <Button
              onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default LoanRepaymentPage;