// Surrender/Income Service for backend integration
// Illustration Type ID: 5
// Question IDs: 501, 502, 503
// Option IDs: (50101,50102), (50201,50202,50203,50204), (50301,50302,50303,50304)

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

/**
 * Interface for Surrender/Income illustration data to be saved
 */
export interface SurrenderIncomeIllustrationData {
  policy_id: number;
  
  // Scenario 1: Full Surrender/Income (Question 501)
  income_by_full_surrender: boolean;
  income_by_full_surrender_amount?: number;
  modify_full_surrender: boolean;
  modify_full_surrender_type?: 'age' | 'policyYear' | 'calendarYear';
  modify_full_surrender_age?: number;
  modify_full_surrender_policy_year?: number;
  modify_full_surrender_calendar_year?: number;
  
  // Scenario 2: Withdrawal Model (Question 502)
  fixed_amount_enabled: boolean;
  fixed_amount?: number;
  percent_of_cash_enabled: boolean;
  percent_of_cash?: number;
  flat_annual_enabled: boolean;
  flat_annual_amount?: number;
  increasing_income_enabled: boolean;
  increasing_start_amount?: number;
  increasing_percentage?: number;
  increasing_end_age?: number;
  
  // Scenario 3: Modify Withdrawal (Question 503)
  modify_flat_annual_enabled: boolean;
  modify_flat_annual_type?: 'age' | 'policyYear' | 'calendarYear';
  modify_flat_annual_age_start?: number;
  modify_flat_annual_age_end?: number;
  modify_flat_annual_policy_year_start?: number;
  modify_flat_annual_policy_year_end?: number;
  modify_flat_annual_calendar_year_start?: number;
  modify_flat_annual_calendar_year_end?: number;
  
  // Schedule data for modify by year
  schedule_data?: Array<{
    age: number;
    policy_year: number;
    calendar_year: number;
    withdrawal_amount: number;
  }>;
}

/**
 * Interface for the API request payload
 */
interface SurrenderIncomeApiRequest {
  selected_options: Array<{
    policy_id: number;
    illustration_type_id: number;
    illustration_question_id: number;
    illustration_option_id?: number;
    illustration_starting_age?: number;
    illustration_ending_age?: number;
    new_face_amount?: number;
    new_coverage_option?: string;
    new_premium_amount?: number;
    new_loan_amount?: number;
    new_loan_repayment_amount?: number;
    current_interest_rate?: number;
    guaranteed_interest_rate?: number;
    illustration_interest_rate?: number;
    surrender_amount?: number;
    RETIREMENT_AGE_GOAL?: number;
    is_schedule?: 'YES' | 'NO';
    schedule_object?: Array<{
      age?: number;
      policy_year?: number;
      current_year?: number;
      face_amount?: number;
      premium_amount?: number;
      coverage_options?: string;
      loan_amount?: number;
      surrender_amount?: number;
      loan_repayment_amount?: number;
      illustration_interest_rate?: number;
    }>;
  }>;
}

/**
 * Interface for API response
 */
interface SurrenderIncomeApiResponse {
  status: 'SUCCESS' | 'FAILED';
  message?: string;
}

/**
 * Map Surrender/Income data to API request format
 */
function mapSurrenderIncomeDataToApiRequest(
  data: SurrenderIncomeIllustrationData,
  currentAge: number,
  currentPolicyYear: number
): SurrenderIncomeApiRequest {
  const selected_options = [];

  // Question 501: Full Surrender/Income
  if (data.income_by_full_surrender || data.modify_full_surrender) {
    let optionId = 50101; // Default: Income by full surrender
    let startingAge = currentAge;
    let isSchedule = 'NO';
    let scheduleObject = null;
    let surrenderAmount = data.income_by_full_surrender_amount;

    if (data.modify_full_surrender) {
      optionId = 50102; // Modify full surrender
      if (data.modify_full_surrender_type === 'age') {
        startingAge = data.modify_full_surrender_age || currentAge;
      } else if (data.modify_full_surrender_type === 'policyYear') {
        startingAge = currentAge + (data.modify_full_surrender_policy_year || currentPolicyYear) - currentPolicyYear;
      } else if (data.modify_full_surrender_type === 'calendarYear') {
        const currentYear = new Date().getFullYear();
        startingAge = currentAge + (data.modify_full_surrender_calendar_year || currentYear) - currentYear;
      }
    }

    const option501 = {
      policy_id: data.policy_id,
      illustration_type_id: 5, // Surrender/Income type
      illustration_question_id: 501,
      illustration_option_id: optionId,
      illustration_starting_age: startingAge,
      illustration_ending_age: null,
      new_face_amount: null,
      new_coverage_option: null,
      new_premium_amount: null,
      new_loan_amount: null,
      new_loan_repayment_amount: null,
      current_interest_rate: null,
      guaranteed_interest_rate: null,
      illustration_interest_rate: null,
      surrender_amount: surrenderAmount,
      RETIREMENT_AGE_GOAL: null,
      is_schedule: isSchedule,
      schedule_object: scheduleObject
    };
    selected_options.push(option501);
  }

  // Question 502: Withdrawal Model
  if (data.fixed_amount_enabled || data.percent_of_cash_enabled || data.flat_annual_enabled || data.increasing_income_enabled) {
    let optionId = 50201; // Default: Fixed amount
    let surrenderAmount = data.fixed_amount;

    if (data.percent_of_cash_enabled) {
      optionId = 50202; // Percent of cash
      surrenderAmount = data.percent_of_cash;
    } else if (data.flat_annual_enabled) {
      optionId = 50203; // Flat annual
      surrenderAmount = data.flat_annual_amount;
    } else if (data.increasing_income_enabled) {
      optionId = 50204; // Increasing income
      surrenderAmount = data.increasing_start_amount;
    }

    const option502 = {
      policy_id: data.policy_id,
      illustration_type_id: 5,
      illustration_question_id: 502,
      illustration_option_id: optionId,
      illustration_starting_age: currentAge,
      illustration_ending_age: data.increasing_income_enabled ? data.increasing_end_age : null,
      new_face_amount: null,
      new_coverage_option: null,
      new_premium_amount: null,
      new_loan_amount: null,
      new_loan_repayment_amount: null,
      current_interest_rate: null,
      guaranteed_interest_rate: null,
      illustration_interest_rate: data.increasing_income_enabled ? data.increasing_percentage : null,
      surrender_amount: surrenderAmount,
      RETIREMENT_AGE_GOAL: null,
      is_schedule: 'NO',
      schedule_object: null
    };
    selected_options.push(option502);
  }

  // Question 503: Modify Withdrawal
  if (data.modify_flat_annual_enabled) {
    let optionId = 50301; // Default: Modify by age
    let startingAge = data.modify_flat_annual_age_start || currentAge;
    let endingAge = data.modify_flat_annual_age_end;
    let isSchedule = 'NO';
    let scheduleObject = null;

    if (data.modify_flat_annual_type === 'policyYear') {
      optionId = 50302; // Modify by policy year
      startingAge = currentAge + (data.modify_flat_annual_policy_year_start || currentPolicyYear) - currentPolicyYear;
      endingAge = data.modify_flat_annual_policy_year_end ? 
        currentAge + data.modify_flat_annual_policy_year_end - currentPolicyYear : null;
    } else if (data.modify_flat_annual_type === 'calendarYear') {
      optionId = 50303; // Modify by calendar year
      const currentYear = new Date().getFullYear();
      startingAge = currentAge + (data.modify_flat_annual_calendar_year_start || currentYear) - currentYear;
      endingAge = data.modify_flat_annual_calendar_year_end ? 
        currentAge + data.modify_flat_annual_calendar_year_end - currentYear : null;
    }

    // If schedule data is provided, use it
    if (data.schedule_data && data.schedule_data.length > 0) {
      optionId = 50304; // Schedule-based modification
      isSchedule = 'YES';
      scheduleObject = data.schedule_data.map(item => ({
        age: item.age,
        policy_year: item.policy_year,
        current_year: item.calendar_year,
        surrender_amount: item.withdrawal_amount,
        face_amount: null,
        premium_amount: null,
        coverage_options: null,
        loan_amount: null,
        loan_repayment_amount: null,
        illustration_interest_rate: null
      }));
    }

    const option503 = {
      policy_id: data.policy_id,
      illustration_type_id: 5,
      illustration_question_id: 503,
      illustration_option_id: optionId,
      illustration_starting_age: startingAge,
      illustration_ending_age: endingAge,
      new_face_amount: null,
      new_coverage_option: null,
      new_premium_amount: null,
      new_loan_amount: null,
      new_loan_repayment_amount: null,
      current_interest_rate: null,
      guaranteed_interest_rate: null,
      illustration_interest_rate: null,
      surrender_amount: data.flat_annual_amount,
      RETIREMENT_AGE_GOAL: null,
      is_schedule: isSchedule,
      schedule_object: scheduleObject
    };
    selected_options.push(option503);
  }

  return { selected_options };
}

/**
 * Save Surrender/Income illustration data to the database
 */
export async function saveSurrenderIncomeIllustration(
  data: SurrenderIncomeIllustrationData,
  currentAge: number,
  currentPolicyYear: number
): Promise<SurrenderIncomeApiResponse> {
  try {
    console.log('🔍 Saving Surrender/Income illustration data:', data);

    // Map the data to API request format
    const apiRequest = mapSurrenderIncomeDataToApiRequest(data, currentAge, currentPolicyYear);

    console.log('📤 API Request payload:', JSON.stringify(apiRequest, null, 2));

    // Make API call to backend
    const response = await fetch(`${API_BASE_URL}/api/illustration/store_options`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiRequest),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ API Response:', result);

    return result;
  } catch (error) {
    console.error('❌ Error saving Surrender/Income illustration:', error);
    return {
      status: 'FAILED',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Validate Surrender/Income data before saving
 */
export function validateSurrenderIncomeData(data: SurrenderIncomeIllustrationData): string[] {
  const errors: string[] = [];

  if (!data.policy_id) {
    errors.push('Policy ID is required');
  }

  if (data.income_by_full_surrender && (!data.income_by_full_surrender_amount || data.income_by_full_surrender_amount <= 0)) {
    errors.push('Full surrender amount must be greater than 0 when income by full surrender is enabled');
  }

  if (data.fixed_amount_enabled && (!data.fixed_amount || data.fixed_amount <= 0)) {
    errors.push('Fixed amount must be greater than 0 when fixed amount withdrawal is enabled');
  }

  if (data.percent_of_cash_enabled && (!data.percent_of_cash || data.percent_of_cash <= 0 || data.percent_of_cash > 100)) {
    errors.push('Percent of cash must be between 1 and 100 when percent of cash withdrawal is enabled');
  }

  if (data.flat_annual_enabled && (!data.flat_annual_amount || data.flat_annual_amount <= 0)) {
    errors.push('Flat annual amount must be greater than 0 when flat annual withdrawal is enabled');
  }

  if (data.increasing_income_enabled) {
    if (!data.increasing_start_amount || data.increasing_start_amount <= 0) {
      errors.push('Increasing income start amount must be greater than 0');
    }
    if (!data.increasing_percentage || data.increasing_percentage <= 0) {
      errors.push('Increasing income percentage must be greater than 0');
    }
    if (!data.increasing_end_age || data.increasing_end_age <= 0) {
      errors.push('Increasing income end age must be specified');
    }
  }

  // Check that at least one scenario is selected
  const hasScenario1 = data.income_by_full_surrender || data.modify_full_surrender;
  const hasScenario2 = data.fixed_amount_enabled || data.percent_of_cash_enabled || data.flat_annual_enabled || data.increasing_income_enabled;
  const hasScenario3 = data.modify_flat_annual_enabled;

  if (!hasScenario1 && !hasScenario2 && !hasScenario3) {
    errors.push('At least one surrender/income scenario must be selected');
  }

  return errors;
}
