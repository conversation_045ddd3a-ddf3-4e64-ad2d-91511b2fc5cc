from typing import List
from fastapi import HTT<PERSON>Exception
from flask import request
from app.models.store_selected_illustrations import SelectedOptionsRequest, SelectedOption, IllustrationScheduleObject
from app.db.connection import get_connection
from datetime import datetime

def store_selected_options(request: SelectedOptionsRequest):
    print(f"🔍 Received request to store selected options: {request}")
    conn = get_connection()
    cursor = conn.cursor(dictionary=True)  # use dictionary cursor to access by column name

    try:
        # Step 0: Fetch illustration_id and date_of_illustration from ILLUSTRATION table using policy_id
        fetch_illustration_query = """
            SELECT ILLUSTRATION_ID
            FROM ILLUSTRATION_TABLE
            WHERE POLICY_ID = %s
            ORDER BY DATE_OF_ILLUSTRATION DESC
            LIMIT 1
        """
        policy_id = request.selected_options[0].policy_id
        print(f"🔍 Looking for illustration for policy_id: {policy_id}")
        cursor.execute(fetch_illustration_query, (policy_id,))
        row = cursor.fetchone()
        print(f"🔍 Found illustration row: {row}")

        if not row:
            print(f"⚠️ No illustration found for policy_id: {policy_id}, creating new illustration record")
            # Create a new illustration record
            create_illustration_query = """
                INSERT INTO ILLUSTRATION_TABLE (POLICY_ID)
                VALUES (%s)
            """
            cursor.execute(create_illustration_query, (policy_id,))
            illustration_id = cursor.lastrowid
            date_of_illustration = datetime.now().date()
            print(f"✅ Created new illustration with ID: {illustration_id}")
        else:
            illustration_id = row["ILLUSTRATION_ID"]
            
        

        for option in request.selected_options:
            print(f"🔍 Processing option: type_id={option.illustration_type_id}, question_id={option.illustration_question_id}")
            if option.illustration_question_id is None or option.illustration_type_id is None:
                print(f"⚠️ Skipping option due to missing type_id or question_id")
                continue
            if option.illustration_option_id in [0, None, "0", "null", "None"]:
                option.illustration_option_id = None
             # 🔍 Check foreign key only if value is NOT NULL
            # if option.illustration_option_id is not None:
            #     cursor.execute("""
            #         SELECT 1 FROM ILLUSTRATION_OPTION_TABLE
            #         WHERE ILLUSTRATION_OPTION_ID = %s
            #         """,  (option.illustration_option_id,))
            #     exists = cursor.fetchone()

            #     if not exists:
            #          raise HTTPException(status_code=400, detail=f"Invalid ILLUSTRATION_OPTION_ID: {option.illustration_option_id}")
            # Step 1: Check if an existing scenario of the same type exists for this policy
            check_existing_query = """
                SELECT SCENARIO_ID FROM ILLUSTRATION_SCENARIO_TABLE
                WHERE POLICY_ID = %s AND ILLUSTRATION_TYPE_ID = %s
                ORDER BY SCENARIO_ID DESC LIMIT 1
            """
            cursor.execute(check_existing_query, (option.policy_id, option.illustration_type_id))
            existing_scenario = cursor.fetchone()

            if existing_scenario:
                # Update existing scenario instead of creating a new one
                print(f"DEBUG: Updating existing scenario {existing_scenario['SCENARIO_ID']} for type {option.illustration_type_id}")
                update_query = """
                    UPDATE ILLUSTRATION_SCENARIO_TABLE SET
                        ILLUSTRATION_ID = %s,
                        ILLUSTRATION_QUESTION_ID = %s,
                        ILLUSTRATION_OPTION_ID = %s,
                        ILLUSTRATION_STARTING_AGE = %s,
                        ILLUSTRATION_ENDING_AGE = %s,
                        NEW_FACE_AMOUNT = %s,
                        NEW_COVERAGE_OPTION = %s,
                        NEW_PREMIUM_AMOUNT = %s,
                        NEW_LOAN_AMOUNT = %s,
                        NEW_LOAN_REPAYMENT_AMOUNT = %s,
                        CURRENT_INTEREST_RATE = %s,
                        GUARANTEED_INTEREST_RATE = %s,
                        ILLUSTRATION_INTEREST_RATE = %s,
                        SURRENDER_AMOUNT = %s,
                        RETIREMENT_AGE_GOAL = %s,
                        SCHEDULE = %s
                    WHERE SCENARIO_ID = %s
                """
                cursor.execute(update_query, (
                    illustration_id,
                    option.illustration_question_id,
                    option.illustration_option_id,
                    option.illustration_starting_age,
                    option.illustration_ending_age,
                    option.new_face_amount,
                    option.new_coverage_option,
                    option.new_premium_amount,
                    option.new_loan_amount,
                    option.new_loan_repayment_amount,
                    option.current_interest_rate,
                    option.guaranteed_interest_rate,
                    option.illustration_interest_rate,
                    option.surrender_amount,
                    option.RETIREMENT_AGE_GOAL,
                    option.is_schedule,
                    existing_scenario['SCENARIO_ID']
                ))
                scenario_id = existing_scenario['SCENARIO_ID']
            else:
                # Insert new scenario data
                print(f"DEBUG: Creating new scenario for type {option.illustration_type_id}")
                insert_query = """
                    INSERT INTO ILLUSTRATION_SCENARIO_TABLE (
                        POLICY_ID,
                        ILLUSTRATION_ID,
                        ILLUSTRATION_TYPE_ID,
                        ILLUSTRATION_QUESTION_ID,
                        ILLUSTRATION_OPTION_ID,
                        ILLUSTRATION_STARTING_AGE,
                        ILLUSTRATION_ENDING_AGE,
                        NEW_FACE_AMOUNT,
                        NEW_COVERAGE_OPTION,
                        NEW_PREMIUM_AMOUNT,
                        NEW_LOAN_AMOUNT,
                        NEW_LOAN_REPAYMENT_AMOUNT,
                        CURRENT_INTEREST_RATE,
                        GUARANTEED_INTEREST_RATE,
                        ILLUSTRATION_INTEREST_RATE,
                        SURRENDER_AMOUNT,
                        RETIREMENT_AGE_GOAL,
                        SCHEDULE

                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(insert_query, (
                    option.policy_id,
                    illustration_id,
                    option.illustration_type_id,
                    option.illustration_question_id,
                    option.illustration_option_id,
                    option.illustration_starting_age,
                    option.illustration_ending_age,
                    option.new_face_amount,
                    option.new_coverage_option,
                    option.new_premium_amount,
                    option.new_loan_amount,
                    option.new_loan_repayment_amount,
                    option.current_interest_rate,
                    option.guaranteed_interest_rate,
                    option.illustration_interest_rate,
                    option.surrender_amount,
                    option.RETIREMENT_AGE_GOAL,
                    option.is_schedule
                ))
                scenario_id = cursor.lastrowid

            # Handle schedule if needed
            if option.is_schedule and option.is_schedule.upper() == 'YES':
                if option.schedule_object:
                    store_schedule(scenario_id, option.schedule_object)
                else:
                    raise HTTPException(status_code=400, detail="Schedule marked as YES but no schedule_object provided.")

        conn.commit()
        print(f"✅ Successfully stored {len(request.selected_options)} illustration options")
        return {"status": "SUCCESS"}
    
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error storing selected options: {str(e)}")

    finally:
        cursor.close()
        conn.close()

def store_schedule(scenario_id: int,schedule_object: list[IllustrationScheduleObject]):
    conn = get_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        # Step 1: Fetch policy_id and illustration_id from ILLUSTRATION_SCENARIO_TABLE
        fetch_query = """
            SELECT POLICY_ID, ILLUSTRATION_ID
            FROM ILLUSTRATION_SCENARIO_TABLE
            WHERE SCENARIO_ID = %s
        """
        cursor.execute(fetch_query, (scenario_id,))
        record = cursor.fetchone()

        if not record:
            raise HTTPException(status_code=404, detail="Scenario ID not found in ILLUSTRATION_SCENARIO_TABLE")

        policy_id = record["POLICY_ID"]
        illustration_id = record["ILLUSTRATION_ID"]

        # Step 2: Insert into SCHEDULE_TABLE
        insert_query = """
            INSERT INTO ILLUSTRATION_SCHEDULE_TABLE (
                POLICY_ID,
                ILLUSTRATION_ID,
                SCENARIO_ID,
                AGE,
                POLICY_YEAR,
                CURRENT_YEAR,
                FACE_AMOUNT,
                PREMIUM_AMOUNT,
                COVERAGE_OPTION,
                LOAN_AMOUNT,
                SURRENDER_AMOUNT,
                LOAN_REPAYMENT_AMOUNT,
                ILLUSTRATION_INTEREST_RATE
            ) VALUES (%s, %s, %s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)
        """
        for schedule in schedule_object:
            cursor.execute(insert_query, (
                policy_id,
                illustration_id,
                scenario_id,
                schedule.age,
                schedule.policy_year,
                schedule.current_year,
                schedule.face_amount,
                schedule.premium_amount,
                schedule.coverage_options,
                schedule.loan_amount,
                schedule.surrender_amount,
                schedule.loan_repayment_amount,
                schedule.illustration_interest_rate
            ))

        conn.commit()

    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error in storing schedule: {str(e)}")

    finally:
        cursor.close()
        conn.close()

