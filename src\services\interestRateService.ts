/**
 * Interest Rate Illustration Service - Backend API Integration
 *
 * This service handles saving Interest Rate illustration data to the database
 * using the backend API endpoint for storing illustration scenarios.
 */

// API Base URL - loaded from .env file
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

/**
 * Interface for Interest Rate illustration data to be saved
 */
export interface InterestRateIllustrationData {
  policy_id: number;
  rate_type: string; // 'current', 'guaranteed', 'stress', 'user-defined'
  stress_rate?: number;
  // Schedule data for user-defined rates
  schedule_data?: Array<{
    age: number;
    policy_year: number;
    calendar_year: number;
    interest_rate: number;
  }>;
  // Age range data
  age_range?: {
    start: number;
    end: number;
  };
  // Policy year range data
  policy_year_range?: {
    start: number;
    end: number;
  };
  // Calendar year range data
  calendar_year_range?: {
    start: number;
    end: number;
  };
  selected_type?: 'age' | 'policyYear' | 'calendarYear';
}

/**
 * Interface for the API request payload
 */
interface InterestRateApiRequest {
  selected_options: Array<{
    policy_id: number;
    illustration_type_id: number;
    illustration_question_id: number;
    illustration_option_id?: number;
    illustration_starting_age?: number;
    illustration_ending_age?: number;
    new_face_amount?: number;
    new_coverage_option?: string;
    new_premium_amount?: number;
    new_loan_amount?: number;
    new_loan_repayment_amount?: number;
    current_interest_rate?: number;
    guaranteed_interest_rate?: number;
    illustration_interest_rate?: number;
    surrender_amount?: number;
    RETIREMENT_AGE_GOAL?: number;
    is_schedule?: 'YES' | 'NO';
    schedule_object?: Array<{
      age?: number;
      policy_year?: number;
      current_year?: number;
      face_amount?: number;
      premium_amount?: number;
      coverage_options?: string;
      loan_amount?: number;
      surrender_amount?: number;
      loan_repayment_amount?: number;
      illustration_interest_rate?: number;
    }>;
    value?: string;
  }>;
}

/**
 * Interface for API response
 */
interface InterestRateApiResponse {
  status: 'SUCCESS' | 'FAILED';
  message?: string;
}

/**
 * Map Interest Rate data to API request format
 */
function mapInterestRateDataToApiRequest(
  data: InterestRateIllustrationData,
  currentAge: number,
  currentPolicyYear: number
): InterestRateApiRequest {
  const selected_options = [];

  // Question 401: Interest Rate Scenarios
  let optionId: number;

  // Map rate type to option ID
  switch (data.rate_type) {
    case 'current':
      optionId = 40101; // Current interest/crediting rate
      break;
    case 'guaranteed':
      optionId = 40102; // Guaranteed minimum interest rate
      break;
    case 'stress':
      optionId = 40103; // Stress scenario
      break;
    case 'user-defined':
      optionId = 40104; // User-defined interest rate Schedule
      break;
    default:
      optionId = 40101; // Default to current rate
  }

  const option401 = {
    policy_id: data.policy_id,
    illustration_type_id: 4, // Interest Rate type
    illustration_question_id: 401,
    illustration_option_id: optionId,
    illustration_starting_age: data.age_range?.start || null,
    illustration_ending_age: data.age_range?.end || null,
    new_face_amount: null,
    new_coverage_option: null,
    new_premium_amount: null,
    new_loan_amount: null,
    new_loan_repayment_amount: null,
    current_interest_rate: data.rate_type === 'current' ? null : null, // Will be populated from policy data
    guaranteed_interest_rate: data.rate_type === 'guaranteed' ? null : null, // Will be populated from policy data
    illustration_interest_rate: data.rate_type === 'stress' ? data.stress_rate : null,
    surrender_amount: null,
    RETIREMENT_AGE_GOAL: null,
    is_schedule: (data.rate_type === 'user-defined' && data.schedule_data) ? 'YES' : 'NO',
    schedule_object: (data.rate_type === 'user-defined' && data.schedule_data) ?
      data.schedule_data.map(item => ({
        age: item.age,
        policy_year: item.policy_year,
        current_year: item.calendar_year,
        face_amount: null,
        premium_amount: null,
        coverage_options: null,
        loan_amount: null,
        surrender_amount: null,
        loan_repayment_amount: null,
        illustration_interest_rate: item.interest_rate
      })) : null,
    value: data.rate_type
  };

  selected_options.push(option401);

  return { selected_options };
}

/**
 * Save Interest Rate illustration data to the database
 */
export async function saveInterestRateIllustration(
  data: InterestRateIllustrationData,
  currentAge: number,
  currentPolicyYear: number
): Promise<InterestRateApiResponse> {
  try {
    console.log('🔍 Saving Interest Rate illustration data:', data);

    // Map the data to API request format
    const apiRequest = mapInterestRateDataToApiRequest(data, currentAge, currentPolicyYear);

    console.log('📤 API Request payload:', JSON.stringify(apiRequest, null, 2));

    // Make API call to backend
    const response = await fetch(`${API_BASE_URL}/api/illustration/store_options`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiRequest),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Backend error response:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const result: InterestRateApiResponse = await response.json();
    console.log('✅ Interest Rate illustration saved successfully:', result);

    return result;
  } catch (error) {
    console.error('❌ Error saving Interest Rate illustration:', error);
    throw new Error(
      error instanceof Error
        ? `Failed to save Interest Rate illustration: ${error.message}`
        : 'Failed to save Interest Rate illustration: Unknown error'
    );
  }
}

/**
 * Validate Interest Rate data before saving
 */
export function validateInterestRateData(data: InterestRateIllustrationData): string[] {
  const errors: string[] = [];

  if (!data.policy_id) {
    errors.push('Policy ID is required');
  }

  if (!data.rate_type) {
    errors.push('Rate type is required');
  }

  if (data.rate_type === 'stress' && (!data.stress_rate || data.stress_rate <= 0)) {
    errors.push('Stress rate must be greater than 0 when stress scenario is selected');
  }

  if (data.rate_type === 'user-defined' && (!data.schedule_data || data.schedule_data.length === 0)) {
    errors.push('Schedule data is required for user-defined interest rate');
  }

  return errors;
}

// Helper function to get current age (replace with real logic)
export const calculateCurrentAge = (): number => {
  // This should be replaced with actual logic to calculate current age
  // based on policy data or customer data
  return 40;
};

// Helper function to get current policy year (replace with real logic)
export const calculateCurrentPolicyYear = (): number => {
  // This should be replaced with actual logic to calculate current policy year
  // based on policy issue date
  return 1;
};
