
from tkinter import NO, YES
from pydantic import BaseModel, Field
from typing import List, Literal, Optional

class IllustrationScheduleObject(BaseModel):
    age:Optional[int]
    policy_year:Optional[int]
    current_year:Optional[int]
    face_amount:Optional[float]
    premium_amount:Optional[float]
    coverage_options:Optional[str]
    loan_amount:Optional[float]
    surrender_amount:Optional[float]
    loan_repayment_amount:Optional[float]
    illustration_interest_rate:Optional[float]


class SelectedOption(BaseModel):
    policy_id:int
    illustration_type_id: int
    illustration_question_id: int
    illustration_option_id: Optional[int]=Field(None, description="Option_id if it exixts")
    illustration_starting_age:Optional[int] = None
    illustration_ending_age:Optional[int] = None
    new_face_amount:Optional[float] = None
    new_coverage_option:Optional[str] = None
    new_premium_amount:Optional[float] = None
    new_loan_amount:Optional[float] = None
    new_loan_repayment_amount:Optional[float] = None
    current_interest_rate:Optional[float] = None
    guaranteed_interest_rate:Optional[float] = None
    illustration_interest_rate:Optional[float] = None
    surrender_amount:Optional[float] = None
    RETIREMENT_AGE_GOAL:Optional[int] = None
    is_schedule: Optional[Literal['YES', 'NO']] = Field(default='NO')
    schedule_object:Optional[list[IllustrationScheduleObject]] = None
    
    
class SelectedOptionsRequest(BaseModel):
    selected_options: List[SelectedOption]

class StoreSelectedOptionsResponse(BaseModel):
    status: str
