/**
 * Illustration Service - Frontend API Integration
 * 
 * This service handles API calls related to illustration type filtering
 * and backend integration for the illustration system.
 */

// API Base URL - loaded from .env file
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

/**
 * Interface for the backend response containing allowed illustration type IDs
 */
export interface AllowedIllustrationTypesResponse {
  success: boolean;
  allowedTypeIds: number[]; // Array of illustration type IDs (1-6)
  message?: string;
}

/**
 * Interface for the request payload to get allowed illustration types
 */
export interface IllustrationTypesRequest {
  policyId: number;
  customerId: number;
  policyType?: string;
  customerData?: any; // Additional customer/policy data if needed
}

/**
 * Fetches allowed illustration types from the backend
 *
 * @param requestData - Policy and customer information
 * @returns Promise<AllowedIllustrationTypesResponse>
 */
export const fetchAllowedIllustrationTypes = async (
  requestData: IllustrationTypesRequest
): Promise<AllowedIllustrationTypesResponse> => {
  try {
    // 🚀 BACKEND API CALL - Using GET method as defined in backend filter_routes.py
    const url = `${API_BASE_URL}/illustration-options/${requestData.policyId}`;
    console.log('🔍 Making API call to:', url);
    console.log('📋 Request data:', requestData);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error Response:', errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }

    const responseText = await response.text();
    console.log('📄 Raw response text:', responseText);

    let data;
    try {
      data = responseText ? JSON.parse(responseText) : null;
    } catch (parseError) {
      console.error('❌ Failed to parse JSON:', parseError);
      throw new Error('Invalid JSON response from server');
    }

    console.log('📊 Parsed response data:', data);

    if (!data) {
      throw new Error('No data received from server');
    }

    // The backend returns { policy_details, filtered_tree }
    // We need to extract illustration type IDs from the filtered_tree
    const allowedTypeIds = extractTypeIdsFromFilteredTree(data.filtered_tree);

    console.log('✅ Successfully extracted allowed type IDs:', allowedTypeIds);

    // If no types are allowed, fall back to all types to prevent empty navigation
    if (allowedTypeIds.length === 0) {
      console.warn('⚠️ No illustration types allowed by backend, falling back to all types');
      return {
        success: true,
        allowedTypeIds: [1, 2, 3, 4, 5, 6],
        message: 'No types filtered by backend, showing all types'
      };
    }

    return {
      success: true,
      allowedTypeIds: allowedTypeIds,
      message: 'Illustration types filtered successfully'
    };

  } catch (error: any) {
    console.error('❌ Error fetching allowed illustration types from backend:', error);
    // Return failure status - context will handle fallback to all types
    return {
      success: false,
      allowedTypeIds: [1, 2, 3, 4, 5, 6], // Fallback to all types on error
      message: `Backend API call failed: ${error.message}`
    };
  }

};

/**
 * Helper function to extract type IDs from the backend's filtered_tree response
 * The filtered_tree contains illustration types with Display_flag indicating if they should be shown
 */
const extractTypeIdsFromFilteredTree = (filteredTree: any): number[] => {
  const allowedTypeIds: number[] = [];

  // The backend returns { count, data } structure
  const dataArray = filteredTree?.data || filteredTree;

  if (!dataArray || !Array.isArray(dataArray)) {
    console.log('❌ Invalid filtered_tree structure:', filteredTree);
    return allowedTypeIds;
  }

  console.log('🔍 Processing filtered_tree data:', dataArray);

  // The backend returns an array of illustration type objects
  for (const typeItem of dataArray) {
    if (typeof typeItem === 'object' && typeItem !== null) {
      console.log(`  📋 Type ${typeItem.ILL_type_id}: Display_flag = ${typeItem.Display_flag}`);

      // Check if this is an illustration type with Display_flag: 'Y'
      if (typeItem.ILL_type_id && typeItem.Display_flag === 'Y') {
        const typeId = parseInt(typeItem.ILL_type_id);
        if (typeId >= 1 && typeId <= 6) {
          allowedTypeIds.push(typeId);
          console.log(`  ✅ Added type ${typeId} to allowed list`);
        }
      }
    }
  }

  console.log('🎯 Final allowed type IDs:', allowedTypeIds);
  return allowedTypeIds;
};




