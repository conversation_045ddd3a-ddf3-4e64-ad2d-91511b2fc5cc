from fastapi import APIRouter, HTTPException, Query
from app.models.disclosure_models import DisclosureResponse
from app.services.disclosure_services import get_disclosure_data

disclosure_router = APIRouter()

@disclosure_router.get("/disclosures", response_model=DisclosureResponse)
def get_disclosures(
    policyId: str = Query(..., description="Policy ID"),
    customerId: str = Query(..., description="Customer ID"), 
    policyType: str = Query(..., description="Policy Type")
):
    """
    Get disclosure data for a specific policy
    """
    try:
        print(f"🔍 Disclosure endpoint called with policyId={policyId}, customerId={customerId}, policyType={policyType}")
        
        result = get_disclosure_data(policyId, customerId, policyType)
        
        if not result.success:
            raise HTTPException(status_code=500, detail=result.message)
            
        return result
        
    except HTTPException as e:
        raise e
    except Exception as e:
        print(f"❌ Disclosure route error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
