// Premium Service for backend integration
// Illustration Type ID: 3
// Question IDs: 301, 302, 303
// Option IDs: (30101,30102,30103), (30201,30202), (30301)

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

/**
 * Interface for Premium illustration data to be saved
 */
export interface PremiumIllustrationData {
  policy_id: number;
  
  // Scenario 1: Annual Premium Modification (Question 301)
  current_premium: number;
  change_to_new_amount: boolean;
  new_premium_amount?: number;
  modify_premium_starting_age: boolean;
  modify_premium_age?: number;
  lump_sum_premium: boolean;
  lump_sum_amount?: number;
  
  // Scenario 2: Premium Payment Cessation (Question 302)
  stop_premium_now: boolean;
  stop_premium_age?: number;
  modify_stop_premium: boolean;
  modify_stop_premium_age?: number;
  modify_stop_premium_policy_year?: number;
  modify_stop_premium_calendar_year?: number;
  
  // Scenario 3: Cash Value Target Premium Calculation (Question 303)
  cash_value_target?: number;
  
  // Schedule data for modify by year
  schedule_data?: Array<{
    age: number;
    policy_year: number;
    calendar_year: number;
    premium_amount: number;
  }>;
}

/**
 * Interface for the API request payload
 */
interface PremiumApiRequest {
  selected_options: Array<{
    policy_id: number;
    illustration_type_id: number;
    illustration_question_id: number;
    illustration_option_id?: number;
    illustration_starting_age?: number;
    illustration_ending_age?: number;
    new_face_amount?: number;
    new_coverage_option?: string;
    new_premium_amount?: number;
    new_loan_amount?: number;
    new_loan_repayment_amount?: number;
    current_interest_rate?: number;
    guaranteed_interest_rate?: number;
    illustration_interest_rate?: number;
    surrender_amount?: number;
    RETIREMENT_AGE_GOAL?: number;
    is_schedule?: 'YES' | 'NO';
    schedule_object?: Array<{
      age?: number;
      policy_year?: number;
      current_year?: number;
      face_amount?: number;
      premium_amount?: number;
      coverage_options?: string;
      loan_amount?: number;
      surrender_amount?: number;
      loan_repayment_amount?: number;
      illustration_interest_rate?: number;
    }>;
  }>;
}

/**
 * Interface for API response
 */
interface PremiumApiResponse {
  status: 'SUCCESS' | 'FAILED';
  message?: string;
}

/**
 * Map Premium data to API request format
 */
function mapPremiumDataToApiRequest(
  data: PremiumIllustrationData,
  currentAge: number,
  currentPolicyYear: number
): PremiumApiRequest {
  const selected_options = [];

  // Question 301: Annual Premium Modification
  if (data.change_to_new_amount || data.modify_premium_starting_age || data.lump_sum_premium) {
    let optionId = 30101; // Default: Change to new amount
    let premiumAmount = data.new_premium_amount;
    let startingAge = currentAge;
    let isSchedule = 'NO';
    let scheduleObject = null;

    if (data.lump_sum_premium) {
      optionId = 30103; // Lump sum premium
      premiumAmount = data.lump_sum_amount;
    } else if (data.modify_premium_starting_age) {
      optionId = 30102; // Modify premium starting from age
      startingAge = data.modify_premium_age || currentAge;
    }

    const option301 = {
      policy_id: data.policy_id,
      illustration_type_id: 3, // Premium type
      illustration_question_id: 301,
      illustration_option_id: optionId,
      illustration_starting_age: startingAge,
      illustration_ending_age: null,
      new_face_amount: null,
      new_coverage_option: null,
      new_premium_amount: premiumAmount,
      new_loan_amount: null,
      new_loan_repayment_amount: null,
      current_interest_rate: null,
      guaranteed_interest_rate: null,
      illustration_interest_rate: null,
      surrender_amount: null,
      RETIREMENT_AGE_GOAL: null,
      is_schedule: isSchedule,
      schedule_object: scheduleObject
    };
    selected_options.push(option301);
  }

  // Question 302: Premium Payment Cessation
  if (data.stop_premium_now || data.modify_stop_premium) {
    let optionId = data.stop_premium_now ? 30201 : 30202;
    let startingAge = data.stop_premium_now ? currentAge : data.modify_stop_premium_age;

    const option302 = {
      policy_id: data.policy_id,
      illustration_type_id: 3,
      illustration_question_id: 302,
      illustration_option_id: optionId,
      illustration_starting_age: startingAge,
      illustration_ending_age: null,
      new_face_amount: null,
      new_coverage_option: null,
      new_premium_amount: 0, // Stop premium means 0 premium
      new_loan_amount: null,
      new_loan_repayment_amount: null,
      current_interest_rate: null,
      guaranteed_interest_rate: null,
      illustration_interest_rate: null,
      surrender_amount: null,
      RETIREMENT_AGE_GOAL: null,
      is_schedule: 'NO',
      schedule_object: null
    };
    selected_options.push(option302);
  }

  // Question 303: Cash Value Target Premium Calculation
  if (data.cash_value_target) {
    const option303 = {
      policy_id: data.policy_id,
      illustration_type_id: 3,
      illustration_question_id: 303,
      illustration_option_id: 30301,
      illustration_starting_age: currentAge,
      illustration_ending_age: null,
      new_face_amount: null,
      new_coverage_option: null,
      new_premium_amount: null,
      new_loan_amount: null,
      new_loan_repayment_amount: null,
      current_interest_rate: null,
      guaranteed_interest_rate: null,
      illustration_interest_rate: null,
      surrender_amount: data.cash_value_target,
      RETIREMENT_AGE_GOAL: null,
      is_schedule: 'NO',
      schedule_object: null
    };
    selected_options.push(option303);
  }

  return { selected_options };
}

/**
 * Save Premium illustration data to the database
 */
export async function savePremiumIllustration(
  data: PremiumIllustrationData,
  currentAge: number,
  currentPolicyYear: number
): Promise<PremiumApiResponse> {
  try {
    console.log('🔍 Saving Premium illustration data:', data);

    // Map the data to API request format
    const apiRequest = mapPremiumDataToApiRequest(data, currentAge, currentPolicyYear);

    console.log('📤 API Request payload:', JSON.stringify(apiRequest, null, 2));

    // Make API call to backend
    const response = await fetch(`${API_BASE_URL}/api/illustration/store_options`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiRequest),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ API Response:', result);

    return result;
  } catch (error) {
    console.error('❌ Error saving Premium illustration:', error);
    return {
      status: 'FAILED',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Validate Premium data before saving
 */
export function validatePremiumData(data: PremiumIllustrationData): string[] {
  const errors: string[] = [];

  if (!data.policy_id) {
    errors.push('Policy ID is required');
  }

  if (!data.current_premium || data.current_premium <= 0) {
    errors.push('Current premium must be greater than 0');
  }

  if (data.change_to_new_amount && (!data.new_premium_amount || data.new_premium_amount <= 0)) {
    errors.push('New premium amount must be greater than 0 when changing to new amount');
  }

  if (data.modify_premium_starting_age && (!data.modify_premium_age || data.modify_premium_age <= 0)) {
    errors.push('Premium modification age must be specified when modifying starting from age');
  }

  if (data.lump_sum_premium && (!data.lump_sum_amount || data.lump_sum_amount <= 0)) {
    errors.push('Lump sum amount must be greater than 0 when selecting lump sum premium');
  }

  if (data.modify_stop_premium && (!data.modify_stop_premium_age || data.modify_stop_premium_age <= 0)) {
    errors.push('Stop premium age must be specified when modifying stop premium');
  }

  if (data.cash_value_target && data.cash_value_target <= 0) {
    errors.push('Cash value target must be greater than 0');
  }

  // Check that at least one scenario is selected
  const hasScenario1 = data.change_to_new_amount || data.modify_premium_starting_age || data.lump_sum_premium;
  const hasScenario2 = data.stop_premium_now || data.modify_stop_premium;
  const hasScenario3 = data.cash_value_target && data.cash_value_target > 0;

  if (!hasScenario1 && !hasScenario2 && !hasScenario3) {
    errors.push('At least one premium scenario must be selected');
  }

  return errors;
}
