/**
 * Loan Repayment Illustration Service - Backend API Integration
 * 
 * This service handles saving Loan Repayment illustration data to the database
 * using the backend API endpoint for storing illustration scenarios.
 */

// API Base URL - loaded from .env file
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

/**
 * Interface for Loan Repayment illustration data to be saved
 */
export interface LoanRepaymentIllustrationData {
  policy_id: number;
  repayment_type: string; // 'new-loan-amount', 'modify-age', 'lump-sum', 'interest-only'
  new_loan_amount?: number;
  lump_sum_amount?: number;
  // Schedule data for modify by year
  schedule_data?: Array<{
    age: number;
    policy_year: number;
    calendar_year: number;
    repayment_amount: number;
  }>;
  // Age range data
  age_range?: {
    start: number;
    end: number;
  };
  // Policy year range data
  policy_year_range?: {
    start: number;
    end: number;
  };
  // Calendar year range data
  calendar_year_range?: {
    start: number;
    end: number;
  };
  selected_type?: 'age' | 'policyYear' | 'calendarYear';
}

/**
 * Interface for the API request payload
 */
interface LoanRepaymentApiRequest {
  selected_options: Array<{
    policy_id: number;
    illustration_type_id: number;
    illustration_question_id: number;
    illustration_option_id?: number;
    illustration_starting_age?: number;
    illustration_ending_age?: number;
    new_face_amount?: number;
    new_coverage_option?: string;
    new_premium_amount?: number;
    new_loan_amount?: number;
    new_loan_repayment_amount?: number;
    current_interest_rate?: number;
    guaranteed_interest_rate?: number;
    illustration_interest_rate?: number;
    surrender_amount?: number;
    RETIREMENT_AGE_GOAL?: number;
    is_schedule?: 'YES' | 'NO';
    schedule_object?: Array<{
      age?: number;
      policy_year?: number;
      current_year?: number;
      face_amount?: number;
      premium_amount?: number;
      coverage_options?: string;
      loan_amount?: number;
      surrender_amount?: number;
      loan_repayment_amount?: number;
      illustration_interest_rate?: number;
    }>;
    value?: string;
  }>;
}

/**
 * Interface for API response
 */
interface LoanRepaymentApiResponse {
  status: 'SUCCESS' | 'FAILED';
  message?: string;
}

/**
 * Map Loan Repayment data to API request format
 */
function mapLoanRepaymentDataToApiRequest(
  data: LoanRepaymentIllustrationData,
  currentAge: number,
  currentPolicyYear: number
): LoanRepaymentApiRequest {
  const selected_options = [];

  // Question 601: Loan Repayment Strategy
  let optionId: number;
  
  // Map repayment type to option ID
  switch (data.repayment_type) {
    case 'new-loan-amount':
      optionId = 60101; // Change to New Loan Repayment amount now
      break;
    case 'modify-age':
      optionId = 60102; // Modify the loan repayment Schedule
      break;
    case 'lump-sum':
      optionId = 60103; // Lump Sum (One-time premium) now
      break;
    case 'interest-only':
      optionId = 60104; // Interest only Payment
      break;
    default:
      optionId = 60101; // Default to first option
  }

  const option601 = {
    policy_id: data.policy_id,
    illustration_type_id: 6, // Loan Repayment type (correct ID)
    illustration_question_id: 601,
    illustration_option_id: optionId,
    illustration_starting_age: data.age_range?.start || null,
    illustration_ending_age: data.age_range?.end || null,
    new_face_amount: null,
    new_coverage_option: null,
    new_premium_amount: null,
    new_loan_amount: data.new_loan_amount || null,
    new_loan_repayment_amount: data.lump_sum_amount || null,
    current_interest_rate: null,
    guaranteed_interest_rate: null,
    illustration_interest_rate: null,
    surrender_amount: null,
    RETIREMENT_AGE_GOAL: null,
    is_schedule: (data.repayment_type === 'modify-age' && data.schedule_data) ? 'YES' : 'NO',
    schedule_object: (data.repayment_type === 'modify-age' && data.schedule_data) ?
      data.schedule_data.map(item => ({
        age: item.age,
        policy_year: item.policy_year,
        current_year: item.calendar_year,
        face_amount: null,
        premium_amount: null,
        coverage_options: null,
        loan_amount: null,
        surrender_amount: null,
        loan_repayment_amount: item.repayment_amount,
        illustration_interest_rate: null
      })) : null,
    value: data.repayment_type
  };
  
  selected_options.push(option601);

  return { selected_options };
}

/**
 * Save Loan Repayment illustration data to the database
 */
export async function saveLoanRepaymentIllustration(
  data: LoanRepaymentIllustrationData,
  currentAge: number,
  currentPolicyYear: number
): Promise<LoanRepaymentApiResponse> {
  try {
    console.log('🔍 Saving Loan Repayment illustration data:', data);

    // Map the data to API request format
    const apiRequest = mapLoanRepaymentDataToApiRequest(data, currentAge, currentPolicyYear);

    console.log('📤 API Request payload:', JSON.stringify(apiRequest, null, 2));

    // Make API call to backend
    const response = await fetch(`${API_BASE_URL}/api/illustration/store_options`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiRequest),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Backend error response:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const result: LoanRepaymentApiResponse = await response.json();
    console.log('✅ Loan Repayment illustration saved successfully:', result);

    return result;
  } catch (error) {
    console.error('❌ Error saving Loan Repayment illustration:', error);
    throw new Error(
      error instanceof Error 
        ? `Failed to save Loan Repayment illustration: ${error.message}`
        : 'Failed to save Loan Repayment illustration: Unknown error'
    );
  }
}

/**
 * Validate Loan Repayment data before saving
 */
export function validateLoanRepaymentData(data: LoanRepaymentIllustrationData): string[] {
  const errors: string[] = [];

  if (!data.policy_id) {
    errors.push('Policy ID is required');
  }

  if (!data.repayment_type) {
    errors.push('Repayment type is required');
  }

  if (data.repayment_type === 'new-loan-amount' && (!data.new_loan_amount || data.new_loan_amount <= 0)) {
    errors.push('New loan amount must be greater than 0 when changing loan amount');
  }

  if (data.repayment_type === 'lump-sum' && (!data.lump_sum_amount || data.lump_sum_amount <= 0)) {
    errors.push('Lump sum amount must be greater than 0 when selecting lump sum option');
  }

  if (data.repayment_type === 'modify-age' && (!data.schedule_data || data.schedule_data.length === 0)) {
    errors.push('Schedule data is required when modifying repayment schedule');
  }

  return errors;
}
