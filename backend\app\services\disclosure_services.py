from typing import List
from app.models.disclosure_models import DisclosureItem, DisclosureResponse

def get_disclosure_data(policy_id: str, customer_id: str, policy_type: str) -> DisclosureResponse:
    """
    Get disclosure data for a policy.
    Since there's no disclosure table in the database yet, this returns sample data.
    """
    try:
        print(f"🔍 Fetching disclosure data for policy_id={policy_id}, customer_id={customer_id}, policy_type={policy_type}")
        
        # Sample disclosure data - this should be replaced with actual database query when disclosure table is available
        sample_disclosures = [
            DisclosureItem(
                DISCLOSURE_ID=1,
                TYPE="About In-Force Illustration",
                DISCLOSURE="This illustration is intended to assist you in understanding how your policy may perform over time. It is based on current assumptions and is not a guarantee of future performance. Actual results may vary."
            ),
            DisclosureItem(
                DISCLOSURE_ID=2,
                TYPE="Premiums You Pay",
                DISCLOSURE="Your policy accepts flexible premiums. Paying minimum required premiums keeps the policy active but may reduce cash values and death benefits. Higher premiums may increase these values."
            ),
            DisclosureItem(
                DISCLOSURE_ID=3,
                TYPE="Interest Rates",
                DISCLOSURE="The illustration shows both guaranteed minimum rates and current rates. Current rates are not guaranteed and may change. The guaranteed minimum rate is the lowest rate your policy will earn."
            ),
            DisclosureItem(
                DISCLOSURE_ID=4,
                TYPE="Policy Loans",
                DISCLOSURE="Policy loans reduce the death benefit and cash surrender value by the amount of the loan plus interest. Unpaid loan interest is added to the loan balance and also reduces the death benefit and cash value."
            ),
            DisclosureItem(
                DISCLOSURE_ID=5,
                TYPE="Surrender Charges",
                DISCLOSURE="Surrender charges may apply if you surrender your policy or take withdrawals during the early years. These charges decrease over time and may eventually be eliminated."
            ),
            DisclosureItem(
                DISCLOSURE_ID=6,
                TYPE="Tax Considerations",
                DISCLOSURE="This illustration does not reflect the tax consequences of the transactions shown. Consult your tax advisor regarding the tax treatment of life insurance in your particular circumstances."
            ),
            DisclosureItem(
                DISCLOSURE_ID=7,
                TYPE="Policy Performance",
                DISCLOSURE="The performance shown assumes that premiums are paid as illustrated and that no loans, withdrawals, or other changes are made to the policy. Any changes may significantly affect the illustrated values."
            ),
            DisclosureItem(
                DISCLOSURE_ID=8,
                TYPE="Regulatory Notice",
                DISCLOSURE="This illustration complies with current regulatory requirements. It is designed to show how the policy may perform under various scenarios but should not be considered as investment advice."
            )
        ]
        
        print(f"✅ Returning {len(sample_disclosures)} disclosure items")
        
        return DisclosureResponse(
            success=True,
            data=sample_disclosures,
            message="Disclosure data fetched successfully"
        )
        
    except Exception as e:
        print(f"❌ Error fetching disclosure data: {str(e)}")
        return DisclosureResponse(
            success=False,
            data=[],
            message=f"Failed to fetch disclosure data: {str(e)}"
        )
