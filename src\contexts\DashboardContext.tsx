import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { Policy, PolicySearchFormData, Scenario, DashboardState, SelectedCustomerData, SelectedPolicyData } from '../types';
import { fetchAllowedIllustrationTypes, IllustrationTypesRequest } from '../services/illustrationService';
import { fetchPolicyScenarios } from '../services/scenarioService';

interface DashboardContextType extends DashboardState {
  setActiveTab: (tab: string) => void;
  setCurrentPolicy: (policy: Policy | null) => void;
  setSelectedCustomerData: (customerData: SelectedCustomerData | null) => void;
  setSelectedPolicyData: (policyData: SelectedPolicyData | null) => void;
  setPolicySearchFormData: (formData: PolicySearchFormData | null) => void;
  addScenario: (scenario: Scenario) => Promise<string | null>;
  updateScenario: (id: string, updates: Partial<Scenario>) => Promise<void>;
  deleteScenario: (id: string) => Promise<void>;
  deleteMultipleScenarios: (ids: string[]) => Promise<void>;
  toggleScenarioSelection: (id: string) => void;
  selectAllScenarios: (select: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setAllowedIllustrationTypes: (typeIds: number[]) => void;
  fetchAllowedIllustrationTypes: (policyId: number, customerId: number, policyType?: string) => Promise<void>;
  loadScenariosFromBackend: (policyId: number) => Promise<void>;
  refreshScenarios: () => Promise<void>;
  clearAllData: () => void; // New function to clear all data
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

const initialState: DashboardState = {
  activeTab: 'policy-selection',
  currentPolicy: null,
  scenarios: [],
  selectedScenarios: [],
  selectedCustomerData: null,
  selectedPolicyData: null,
  policySearchFormData: null,
  loading: false,
  error: null,
  allowedIllustrationTypes: [], // Start empty until backend filtering is applied
};

type DashboardAction =
  | { type: 'SET_ACTIVE_TAB'; payload: string }
  | { type: 'SET_CURRENT_POLICY'; payload: Policy | null }
  | { type: 'SET_SELECTED_CUSTOMER_DATA'; payload: SelectedCustomerData | null }
  | { type: 'SET_SELECTED_POLICY_DATA'; payload: SelectedPolicyData | null }
  | { type: 'SET_POLICY_SEARCH_FORM_DATA'; payload: PolicySearchFormData | null }
  | { type: 'ADD_SCENARIO'; payload: Scenario }
  | { type: 'SET_SCENARIOS'; payload: Scenario[] }
  | { type: 'UPDATE_SCENARIO'; payload: { id: string; updates: Partial<Scenario> } }
  | { type: 'DELETE_SCENARIO'; payload: string }
  | { type: 'DELETE_MULTIPLE_SCENARIOS'; payload: string[] }
  | { type: 'TOGGLE_SCENARIO_SELECTION'; payload: string }
  | { type: 'SET_SELECTED_SCENARIOS'; payload: string[] }
  | { type: 'SELECT_ALL_SCENARIOS'; payload: boolean }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_ALLOWED_ILLUSTRATION_TYPES'; payload: number[] };

const dashboardReducer = (state: DashboardState, action: DashboardAction): DashboardState => {
  switch (action.type) {
    case 'SET_ACTIVE_TAB':
      return { ...state, activeTab: action.payload };
    case 'SET_CURRENT_POLICY':
      return { ...state, currentPolicy: action.payload };
    case 'SET_SELECTED_CUSTOMER_DATA':
      return { ...state, selectedCustomerData: action.payload };
    case 'SET_SELECTED_POLICY_DATA':
      return { ...state, selectedPolicyData: action.payload };
    case 'SET_POLICY_SEARCH_FORM_DATA':
      return { ...state, policySearchFormData: action.payload };
    case 'ADD_SCENARIO':
      return { ...state, scenarios: [...state.scenarios, action.payload] };
    case 'SET_SCENARIOS':
      return { ...state, scenarios: action.payload };
    case 'UPDATE_SCENARIO':
      return {
        ...state,
        scenarios: state.scenarios.map(scenario =>
          scenario.id === action.payload.id
            ? { ...scenario, ...action.payload.updates }
            : scenario
        ),
      };
    case 'DELETE_SCENARIO':
      return {
        ...state,
        scenarios: state.scenarios.filter(scenario => scenario.id !== action.payload),
        selectedScenarios: state.selectedScenarios.filter(id => id !== action.payload),
      };
    case 'DELETE_MULTIPLE_SCENARIOS':
      return {
        ...state,
        scenarios: state.scenarios.filter(scenario => !action.payload.includes(scenario.id)),
        selectedScenarios: [],
      };
    case 'TOGGLE_SCENARIO_SELECTION':
      return {
        ...state,
        selectedScenarios: state.selectedScenarios.includes(action.payload)
          ? state.selectedScenarios.filter(id => id !== action.payload)
          : [...state.selectedScenarios, action.payload],
      };
    case 'SET_SELECTED_SCENARIOS':
      return {
        ...state,
        selectedScenarios: action.payload,
      };
    case 'SELECT_ALL_SCENARIOS':
      return {
        ...state,
        selectedScenarios: action.payload ? state.scenarios.map(s => s.id) : [],
      };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_ALLOWED_ILLUSTRATION_TYPES':
      return { ...state, allowedIllustrationTypes: action.payload };
    default:
      return state;
  }
};

export const DashboardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(dashboardReducer, initialState);

  // Initialize with completely fresh state - no data persistence across sessions
  useEffect(() => {
    const initializeData = async () => {
      dispatch({ type: 'SET_LOADING', payload: false });
      dispatch({ type: 'SET_ERROR', payload: null });

      // Always start completely fresh - no scenarios, no policy data
      dispatch({ type: 'SET_SCENARIOS', payload: [] });
      dispatch({ type: 'SET_SELECTED_SCENARIOS', payload: [] });
      dispatch({ type: 'SET_SELECTED_POLICY_DATA', payload: null });
      dispatch({ type: 'SET_SELECTED_CUSTOMER_DATA', payload: null });
      dispatch({ type: 'SET_POLICY_SEARCH_FORM_DATA', payload: null });
      dispatch({ type: 'SET_CURRENT_POLICY', payload: null });
      dispatch({ type: 'SET_ACTIVE_TAB', payload: 'policy-selection' });

      console.log('🔄 Dashboard initialized with completely fresh state. No data from previous sessions.');
    };

    initializeData();
  }, []);

  // No localStorage auto-save - all data is managed through backend

  return (
    <DashboardContext.Provider value={{
      ...state,
      setActiveTab: (tab: string) => dispatch({ type: 'SET_ACTIVE_TAB', payload: tab }),
      setCurrentPolicy: (policy: Policy | null) => dispatch({ type: 'SET_CURRENT_POLICY', payload: policy }),
      setSelectedCustomerData: (customerData: SelectedCustomerData | null) => dispatch({ type: 'SET_SELECTED_CUSTOMER_DATA', payload: customerData }),
      setSelectedPolicyData: (policyData: SelectedPolicyData | null) => dispatch({ type: 'SET_SELECTED_POLICY_DATA', payload: policyData }),
      setPolicySearchFormData: (formData: PolicySearchFormData | null) => dispatch({ type: 'SET_POLICY_SEARCH_FORM_DATA', payload: formData }),
      addScenario: async (scenario: Scenario) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });

          // Add the scenario to current session state for immediate display
          // This maintains fresh start (no old scenarios) but shows current session scenarios
          console.log('✅ Adding scenario to current session:', scenario.name);

          // Generate a unique ID for the current session
          const sessionScenarioId = 'session-' + Date.now().toString();
          const sessionScenario = {
            ...scenario,
            id: sessionScenarioId
          };

          // Add to current session scenarios (immediate display)
          dispatch({ type: 'ADD_SCENARIO', payload: sessionScenario });

          console.log('✅ Scenario added to current session and will display immediately');
          return sessionScenarioId;
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : 'Failed to create scenario';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to create scenario:', error);
          return null;
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      updateScenario: async (id: string, updates: Partial<Scenario>) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          // TODO: Implement backend scenario update API
          console.warn('⚠️ Scenario update not implemented - backend API needed');
          dispatch({ type: 'UPDATE_SCENARIO', payload: { id, updates } });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : 'Failed to update scenario';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to update scenario:', error);
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      deleteScenario: async (id: string) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          // TODO: Implement backend scenario deletion API
          console.warn('⚠️ Scenario deletion not implemented - backend API needed');
          dispatch({ type: 'DELETE_SCENARIO', payload: id });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : 'Failed to delete scenario';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to delete scenario:', error);
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      deleteMultipleScenarios: async (ids: string[]) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          // TODO: Implement backend multiple scenario deletion API
          console.warn('⚠️ Multiple scenario deletion not implemented - backend API needed');
          dispatch({ type: 'DELETE_MULTIPLE_SCENARIOS', payload: ids });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : 'Failed to delete scenarios';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to delete multiple scenarios:', error);
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      toggleScenarioSelection: (id: string) => dispatch({ type: 'TOGGLE_SCENARIO_SELECTION', payload: id }),
      selectAllScenarios: (select: boolean) => dispatch({ type: 'SELECT_ALL_SCENARIOS', payload: select }),
      setLoading: (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: loading }),
      setError: (error: string | null) => dispatch({ type: 'SET_ERROR', payload: error }),
      setAllowedIllustrationTypes: (typeIds: number[]) => dispatch({ type: 'SET_ALLOWED_ILLUSTRATION_TYPES', payload: typeIds }),
      fetchAllowedIllustrationTypes: async (policyId: number, customerId: number, policyType?: string) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          dispatch({ type: 'SET_ERROR', payload: null });

          const requestData: IllustrationTypesRequest = {
            policyId,
            customerId,
            policyType,
          };

          const response = await fetchAllowedIllustrationTypes(requestData);

          console.log('🔍 API Response:', response.success ? '✅ SUCCESS' : '❌ FAILED');
          console.log('📊 Allowed Type IDs:', response.allowedTypeIds);
          console.log('💬 Message:', response.message);

          if (response.success) {
            // Ensure we have at least some types
            const typeIds = response.allowedTypeIds.length > 0 ? response.allowedTypeIds : [1, 2, 3, 4, 5, 6];
            dispatch({ type: 'SET_ALLOWED_ILLUSTRATION_TYPES', payload: typeIds });
            console.log('✅ Set allowed illustration types:', typeIds);
          } else {
            console.warn('❌ Backend failed:', response.message);
            dispatch({ type: 'SET_ERROR', payload: response.message || 'Failed to fetch allowed illustration types' });
            // Keep default types on error
            dispatch({ type: 'SET_ALLOWED_ILLUSTRATION_TYPES', payload: [1, 2, 3, 4, 5, 6] });
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch allowed illustration types';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Error fetching allowed illustration types:', error);
          // Keep default types on error
          dispatch({ type: 'SET_ALLOWED_ILLUSTRATION_TYPES', payload: [1, 2, 3, 4, 5, 6] });
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      loadScenariosFromBackend: async (policyId: number) => {
        // DO NOT LOAD OLD SCENARIOS - Always start fresh after login
        console.log('🚫 loadScenariosFromBackend called but DISABLED for fresh start');
        console.log('🔄 Policy ID:', policyId, '- Starting with empty scenarios for fresh illustration');

        // Always start with empty scenarios - no loading from backend
        dispatch({ type: 'SET_SCENARIOS', payload: [] });
        dispatch({ type: 'SET_SELECTED_SCENARIOS', payload: [] });
        dispatch({ type: 'SET_LOADING', payload: false });
        dispatch({ type: 'SET_ERROR', payload: null });

        console.log('✅ Fresh start: No old scenarios loaded. User must create new illustrations.');
      },
      refreshScenarios: async () => {
        // DO NOT REFRESH OLD SCENARIOS - Always keep fresh start
        console.log('🚫 refreshScenarios called but DISABLED for fresh start');
        console.log('🔄 Keeping empty scenarios for fresh illustration experience');

        // Always maintain empty scenarios - no loading from backend
        dispatch({ type: 'SET_SCENARIOS', payload: [] });
        dispatch({ type: 'SET_SELECTED_SCENARIOS', payload: [] });
        dispatch({ type: 'SET_LOADING', payload: false });

        console.log('✅ Refresh completed: Maintaining fresh start with no old scenarios');
      },
      clearAllData: () => {
        // Clear all dashboard data for fresh start
        dispatch({ type: 'SET_SCENARIOS', payload: [] });
        dispatch({ type: 'SET_SELECTED_SCENARIOS', payload: [] });
        dispatch({ type: 'SET_SELECTED_POLICY_DATA', payload: null });
        dispatch({ type: 'SET_SELECTED_CUSTOMER_DATA', payload: null });
        dispatch({ type: 'SET_POLICY_SEARCH_FORM_DATA', payload: null });
        dispatch({ type: 'SET_CURRENT_POLICY', payload: null });
        dispatch({ type: 'SET_ACTIVE_TAB', payload: 'policy-selection' });
        dispatch({ type: 'SET_LOADING', payload: false });
        dispatch({ type: 'SET_ERROR', payload: null });
        console.log('🧹 All dashboard data cleared for fresh start');
      },
    }}>
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboard = (): DashboardContextType => {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};