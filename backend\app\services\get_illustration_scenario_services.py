from fastapi import HTTPException
from app.db.connection import get_connection
from app.models.get_illustration_scenario_models import (
    GetIllustrationScenarioResponse,
    IllustrationObject,
    IllustrationTypeOut,
    IllustrationQuestionOut,
    IllustrationOptionOut,
    ScenarioSummary,
    GetPolicyScenariosResponse
)

# def get_illustration_scenario_service(policy_id: int, scenario_id: int) -> GetIllustrationScenarioResponse:
#     connection = get_connection()
#     cursor = connection.cursor(dictionary=True)

#     try:
#         # Step 1: Fetch scenario data
#         cursor.execute("""
#             SELECT * FROM ILLUSTRATION_SCENARIO_TABLE
#             WHERE POLICY_ID = %s AND SCENARIO_ID = %s
#         """, (policy_id, scenario_id))

#         scenario_row = cursor.fetchone()
#         if not scenario_row:
#             raise HTTPException(status_code=404, detail="No scenario found")

#         # Extract IDs
#         type_id = scenario_row["ILLUSTRATION_TYPE_ID"]
#         question_id = scenario_row["ILLUSTRATION_QUESTION_ID"]
#         option_id = scenario_row["ILLUSTRATION_OPTION_ID"]

#         # Step 2: Fetch type description
#         cursor.execute("""
#             SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_TYPE_TABLE
#             WHERE ILLUSTRATION_TYPE_ID = %s
#         """, (type_id,))
#         type_description = cursor.fetchone()
#         if not type_description:
#             type_description = {"SHORT_DESCRIPTION": ""}

#         # Step 3: Fetch question description
#         cursor.execute("""
#             SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_QUESTION_TABLE
#             WHERE ILLUSTRATION_QUESTION_ID = %s
#         """, (question_id,))
#         question_description = cursor.fetchone()
#         if not question_description:
#             question_description = {"SHORT_DESCRIPTION": ""}

#         # Step 4: Fetch option description
#         cursor.execute("""
#             SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_OPTION_TABLE
#             WHERE ILLUSTRATION_OPTION_ID = %s
#         """, (option_id,))
#         option_description = cursor.fetchone()
#         if not option_description:
#             option_description = {"SHORT_DESCRIPTION": ""}

#         # Step 5: Prepare the nested structure
#         option_obj = IllustrationOptionOut(
#             option_id=option_id,
#             option_description=option_description["SHORT_DESCRIPTION"]
#         )

#         question_obj = IllustrationQuestionOut(
#             question_id=question_id,
#             question_description=question_description["SHORT_DESCRIPTION"],
#             options=[option_obj]
#         )

#         type_obj = IllustrationTypeOut(
#             type_id=type_id,
#             type_description=type_description["SHORT_DESCRIPTION"],
#             questions=[question_obj]
#         )

#         illustration_object = IllustrationObject(
#             policy_id=policy_id,
#             scenario_id=scenario_id,
#             illustration_options=[type_obj]
#         )

#         # Step 6: Build final response
#         return GetIllustrationScenarioResponse(
#             illustration=[illustration_object],
#             policy_id=policy_id,
#             scenario_id=scenario_id,
#             illustration_id=scenario_row["ILLUSTRATION_ID"],
#             date_of_illustration=scenario_row["DATE_OF_ILLUSTRATION"],
#             illustration_type_id=scenario_row["ILLUSTRATION_TYPE_ID"],
#             illustration_question_id=scenario_row["ILLUSTRATION_QUESTION_ID"],
#             illustration_option_id=scenario_row["ILLUSTRATION_OPTION_ID"],
#             illustration_starting_age=scenario_row["ILLUSTRATION_STARTING_AGE"],
#             illustration_ending_age=scenario_row["ILLUSTRATION_ENDING_AGE"],
#             new_face_amount=scenario_row["NEW_FACE_AMOUNT"],
#             new_coverage_option=scenario_row["NEW_COVERAGE_OPTION"],
#             new_premium_amount=scenario_row["NEW_PREMIUM_AMOUNT"],
#             new_loan_amount=scenario_row["NEW_LOAN_AMOUNT"],
#             new_loan_repayment_amount=scenario_row["NEW_LOAN_REPAYMENT_AMOUNT"],
#             current_interest_rate=scenario_row["CURRENT_INTEREST_RATE"],
#             guaranteed_minimum_rate=scenario_row["GUARANTEED_INTEREST_RATE"],
#             illustration_interest_rate=scenario_row["ILLUSTRATION_INTEREST_RATE"],
#             surrrender_amount=scenario_row["SURRENDER_AMOUNT"],
#             is_schedule=scenario_row["SCHEDULE"]
#         )
#     except Exception as e:
#         # Log error (optional) and raise meaningful exception
#         raise HTTPException(status_code=500, detail=f"Failed to fetch illustration scenario: {str(e)}")

#     finally:
#         cursor.close()
#         connection.close()
# from fastapi import HTTPException





def get_illustration_scenario_service(policy_id: int, scenario_id: int) -> GetIllustrationScenarioResponse:
    connection = get_connection()
    cursor = connection.cursor(dictionary=True)

    try:
        print(f"DEBUG: Fetching scenario for POLICY_ID={policy_id}, SCENARIO_ID={scenario_id}")

        # Step 1: Fetch scenario data with date from ILLUSTRATION_TABLE
        cursor.execute("""
            SELECT s.*, i.DATE_OF_ILLUSTRATION
            FROM ILLUSTRATION_SCENARIO_TABLE s
            LEFT JOIN ILLUSTRATION_TABLE i ON s.ILLUSTRATION_ID = i.ILLUSTRATION_ID
            WHERE s.POLICY_ID = %s AND s.SCENARIO_ID = %s
        """, (policy_id, scenario_id))

        scenario_row = cursor.fetchone()
        print("DEBUG: Scenario Row =", scenario_row)

        if not scenario_row:
            raise HTTPException(status_code=404, detail="No scenario found")

        # Extract IDs
        type_id = scenario_row["ILLUSTRATION_TYPE_ID"]
        question_id = scenario_row["ILLUSTRATION_QUESTION_ID"]
        option_id = scenario_row["ILLUSTRATION_OPTION_ID"]

        print(f"DEBUG: type_id={type_id}, question_id={question_id}, option_id={option_id}")

        # Step 2: Fetch type description
        cursor.execute("""
            SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_TYPE_TABLE
            WHERE ILLUSTRATION_TYPE_ID = %s
        """, (type_id,))
        type_description = cursor.fetchone() or {"SHORT_DESCRIPTION": ""}
        print("DEBUG: Type Description =", type_description)

        # Step 3: Fetch question description
        cursor.execute("""
            SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_QUESTION_TABLE
            WHERE ILLUSTRATION_QUESTION_ID = %s
        """, (question_id,))
        question_description = cursor.fetchone() or {"SHORT_DESCRIPTION": ""}
        print("DEBUG: Question Description =", question_description)

        # Step 4: Clean and validate option_id
        invalid_option_values = {None, 0, "0", "null", "None"}
        option_id_str = str(option_id).strip()
        clean_option_id = None if option_id_str in invalid_option_values else int(option_id)

        print(f"DEBUG: Cleaned Option ID = {clean_option_id} (type: {type(clean_option_id)})")

        # Step 4: Fetch option description safely
        if clean_option_id is not None:
            cursor.execute("""
                SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_OPTION_TABLE
                WHERE ILLUSTRATION_OPTION_ID = %s
            """, (clean_option_id,))
            option_description = cursor.fetchone() or {"SHORT_DESCRIPTION": ""}
        else:
            option_description = {"SHORT_DESCRIPTION": ""}
        print("DEBUG: Option Description =", option_description)

        # Step 5: Prepare the nested structure
        option_obj = IllustrationOptionOut(
            option_id=clean_option_id,
            option_description=option_description["SHORT_DESCRIPTION"]
        )

        print("DEBUG: Created option_obj =", option_obj)

        question_obj = IllustrationQuestionOut(
            question_id=question_id,
            question_description=question_description["SHORT_DESCRIPTION"],
            options=[option_obj]
        )

        type_obj = IllustrationTypeOut(
            type_id=type_id,
            type_description=type_description["SHORT_DESCRIPTION"],
            questions=[question_obj]
        )

        illustration_object = IllustrationObject(
            policy_id=policy_id,
            scenario_id=scenario_id,
            illustration_options=[type_obj]
        )

        # Step 6: Build final response
        response = GetIllustrationScenarioResponse(
            illustration=[illustration_object],
            policy_id=policy_id,
            scenario_id=scenario_id,
            illustration_id=scenario_row["ILLUSTRATION_ID"],
            date_of_illustration=scenario_row["DATE_OF_ILLUSTRATION"],
            illustration_type_id=scenario_row["ILLUSTRATION_TYPE_ID"],
            illustration_question_id=scenario_row["ILLUSTRATION_QUESTION_ID"],
            illustration_option_id=scenario_row["ILLUSTRATION_OPTION_ID"],
            illustration_starting_age=scenario_row["ILLUSTRATION_STARTING_AGE"],
            illustration_ending_age=scenario_row["ILLUSTRATION_ENDING_AGE"],
            new_face_amount=scenario_row["NEW_FACE_AMOUNT"],
            new_coverage_option=scenario_row["NEW_COVERAGE_OPTION"],
            new_premium_amount=scenario_row["NEW_PREMIUM_AMOUNT"],
            new_loan_amount=scenario_row["NEW_LOAN_AMOUNT"],
            new_loan_repayment_amount=scenario_row["NEW_LOAN_REPAYMENT_AMOUNT"],
            current_interest_rate=scenario_row["CURRENT_INTEREST_RATE"],
            guaranteed_minimum_rate=scenario_row["GUARANTEED_INTEREST_RATE"],
            illustration_interest_rate=scenario_row["ILLUSTRATION_INTEREST_RATE"],
            surrrender_amount=scenario_row["SURRENDER_AMOUNT"],
            is_schedule=scenario_row["SCHEDULE"]
        )

        print("DEBUG: Final Response =", response)
        return response

    finally:
        cursor.close()
        connection.close()


def get_policy_scenarios_service(policy_id: int) -> GetPolicyScenariosResponse:
    """
    Retrieve all scenarios for a specific policy ID
    """
    connection = get_connection()
    cursor = connection.cursor(dictionary=True)

    try:
        print(f"DEBUG: Fetching all scenarios for POLICY_ID={policy_id}")

        # Step 1: Fetch all scenarios for the policy with type and question descriptions
        cursor.execute("""
            SELECT
                s.*,
                i.DATE_OF_ILLUSTRATION,
                t.SHORT_DESCRIPTION as type_description,
                q.SHORT_DESCRIPTION as question_description
            FROM ILLUSTRATION_SCENARIO_TABLE s
            LEFT JOIN ILLUSTRATION_TABLE i ON s.ILLUSTRATION_ID = i.ILLUSTRATION_ID
            LEFT JOIN ILLUSTRATION_TYPE_TABLE t ON s.ILLUSTRATION_TYPE_ID = t.ILLUSTRATION_TYPE_ID
            LEFT JOIN ILLUSTRATION_QUESTION_TABLE q ON s.ILLUSTRATION_QUESTION_ID = q.ILLUSTRATION_QUESTION_ID
            WHERE s.POLICY_ID = %s
            ORDER BY s.SCENARIO_ID DESC
        """, (policy_id,))

        scenario_rows = cursor.fetchall()
        print(f"DEBUG: Found {len(scenario_rows)} scenarios for policy {policy_id}")

        scenarios = []
        for row in scenario_rows:
            # Handle option description if option_id exists
            option_description = None
            if row["ILLUSTRATION_OPTION_ID"]:
                cursor.execute("""
                    SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_OPTION_TABLE
                    WHERE ILLUSTRATION_OPTION_ID = %s
                """, (row["ILLUSTRATION_OPTION_ID"],))
                option_result = cursor.fetchone()
                if option_result:
                    option_description = option_result["SHORT_DESCRIPTION"]

            scenario = ScenarioSummary(
                scenario_id=row["SCENARIO_ID"],
                policy_id=row["POLICY_ID"],
                illustration_id=row["ILLUSTRATION_ID"],
                date_of_illustration=row["DATE_OF_ILLUSTRATION"],
                illustration_type_id=row["ILLUSTRATION_TYPE_ID"],
                illustration_type_description=row["type_description"] or "",
                illustration_question_id=row["ILLUSTRATION_QUESTION_ID"],
                illustration_question_description=row["question_description"] or "",
                illustration_option_id=row["ILLUSTRATION_OPTION_ID"],
                illustration_option_description=option_description,
                illustration_starting_age=row["ILLUSTRATION_STARTING_AGE"],
                illustration_ending_age=row["ILLUSTRATION_ENDING_AGE"],
                new_face_amount=row["NEW_FACE_AMOUNT"],
                new_coverage_option=row["NEW_COVERAGE_OPTION"],
                new_premium_amount=row["NEW_PREMIUM_AMOUNT"],
                new_loan_amount=row["NEW_LOAN_AMOUNT"],
                new_loan_repayment_amount=row["NEW_LOAN_REPAYMENT_AMOUNT"],
                current_interest_rate=row["CURRENT_INTEREST_RATE"],
                guaranteed_minimum_rate=row["GUARANTEED_INTEREST_RATE"],
                illustration_interest_rate=row["ILLUSTRATION_INTEREST_RATE"],
                surrender_amount=row["SURRENDER_AMOUNT"],
                retirement_age_goal=row["RETIREMENT_AGE_GOAL"],
                is_schedule=row["SCHEDULE"]
            )
            scenarios.append(scenario)

        response = GetPolicyScenariosResponse(
            policy_id=policy_id,
            scenarios=scenarios,
            total_count=len(scenarios)
        )

        print(f"DEBUG: Returning {len(scenarios)} scenarios for policy {policy_id}")
        return response

    except Exception as e:
        print(f"ERROR: Failed to fetch scenarios for policy {policy_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch scenarios: {str(e)}")

    finally:
        cursor.close()
        connection.close()
