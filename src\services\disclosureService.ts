// Disclosure Service - Only handles disclosure data fetching
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Types for disclosure data
export interface DisclosureItem {
  DISCLOSURE_ID: number;
  TYPE: string;
  DISCLOSURE: string;
}

export interface DisclosureRequest {
  policyId: string;
  customerId: string;
  policyType: string;
}

export interface DisclosureResponse {
  success: boolean;
  data: DisclosureItem[];
  message?: string;
}

/**
 * Fetch disclosure data from backend API
 * @param requestData - The disclosure request parameters
 * @returns Promise with disclosure response
 */
export const fetchDisclosureData = async (requestData: DisclosureRequest): Promise<DisclosureResponse> => {
  try {
    console.log('🔍 Fetching disclosure data from:', `${API_BASE_URL}/disclosures`);
    console.log('📋 Request data:', requestData);

    // Convert request data to URL parameters
    const params = new URLSearchParams({
      policyId: requestData.policyId,
      customerId: requestData.customerId,
      policyType: requestData.policyType
    });
    
    const url = `${API_BASE_URL}/disclosures?${params.toString()}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    console.log('� Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error Response:', errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const responseText = await response.text();
    console.log('📄 Raw response text:', responseText);

    let data;
    try {
      data = responseText ? JSON.parse(responseText) : null;
    } catch (parseError) {
      console.error('❌ Failed to parse JSON:', parseError);
      throw new Error('Invalid JSON response from server');
    }

    if (!data) {
      return {
        success: false,
        data: [],
        message: 'No data received from server'
      };
    }

    // Handle different response formats
    let disclosureArray: DisclosureItem[] = [];

    if (Array.isArray(data)) {
      disclosureArray = data;
    } else if (typeof data === 'object') {
      if (data.data && Array.isArray(data.data)) {
        disclosureArray = data.data;
      } else if (data.DISCLOSURE_ID && data.TYPE && data.DISCLOSURE) {
        disclosureArray = [data];
      } else if (data.disclosure_data && Array.isArray(data.disclosure_data)) {
        disclosureArray = data.disclosure_data;
      }
    }

    console.log('📊 Processed disclosure array:', disclosureArray);

    return {
      success: true,
      data: disclosureArray
    };

  } catch (error) {
    console.error('❌ Error in fetchDisclosureData:', error);
    return {
      success: false,
      data: [],
      message: error instanceof Error ? error.message : 'Failed to fetch disclosure data'
    };
  }
};

/**
 * Fetch disclosure data with retry functionality
 * @param requestData - The disclosure request parameters
 * @param retryCount - Number of retry attempts (default: 2)
 * @returns Promise with disclosure response
 */
export const fetchDisclosureDataWithRetry = async (
  requestData: DisclosureRequest,
  retryCount: number = 2
): Promise<DisclosureResponse> => {
  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= retryCount; attempt++) {
    try {
      console.log(`🔄 Disclosure fetch attempt ${attempt + 1}/${retryCount + 1}`);
      const result = await fetchDisclosureData(requestData);

      if (result.success) {
        console.log('✅ Disclosure data fetched successfully');
        return result;
      } else {
        lastError = new Error(result.message || 'Unknown error');
      }
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      console.warn(`⚠️ Disclosure fetch attempt ${attempt + 1} failed:`, lastError.message);

      // Wait before retry (except on last attempt)
      if (attempt < retryCount) {
        await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
      }
    }
  }

  return {
    success: false,
    data: [],
    message: lastError?.message || 'Failed to fetch disclosure data after retries'
  };
};

/**
 * Initialize disclosure data for a policy
 * @param selectedCustomerData - Customer data
 * @param selectedPolicyData - Policy data
 * @returns Promise with disclosure response and loading state handlers
 */
export const initializeDisclosureData = async (
  selectedCustomerData: any,
  selectedPolicyData: any,
  setLoadingDisclosure: (loading: boolean) => void,
  setDisclosureError: (error: string | null) => void,
  setDisclosureData: (data: DisclosureItem[]) => void
): Promise<void> => {
  if (!selectedCustomerData || !selectedPolicyData) {
    setDisclosureData([]);
    return;
  }

  setLoadingDisclosure(true);
  setDisclosureError(null);

  try {
    console.log('🚀 Initializing disclosure data for:', {
      policyId: selectedPolicyData.id || selectedCustomerData.policyNumber || '1',
      customerId: selectedCustomerData.customerId || '1'
    });

    const response = await fetchDisclosureDataWithRetry({
      policyId: selectedPolicyData.id || selectedCustomerData.policyNumber || '1',
      customerId: selectedCustomerData.customerId || '1',
      policyType: 'life-insurance'
    });

    if (response.success && Array.isArray(response.data)) {
      setDisclosureData(response.data);
      console.log('✅ Disclosure data initialized:', response.data);
    } else {
      console.warn('⚠️ Invalid disclosure data format:', response);
      throw new Error(response.message || 'Invalid disclosure data format received');
    }
  } catch (error) {
    console.error('❌ Error initializing disclosure data:', error);
    setDisclosureError(error instanceof Error ? error.message : 'Failed to load disclosure data');
    setDisclosureData([]);
  } finally {
    setLoadingDisclosure(false);
  }
};
