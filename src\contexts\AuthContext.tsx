import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { User, AuthState } from '../types';

interface AuthContextType extends AuthState {
  login: (username: string, password: string, rememberMe: boolean) => Promise<boolean>;
  logout: () => void;
  resetPassword: (email: string) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  rememberMe: false,
};

type AuthAction = 
  | { type: 'LOGIN_SUCCESS'; payload: { user: User; rememberMe: boolean } }
  | { type: 'LOGOUT' }
  | { type: 'RESTORE_SESSION'; payload: { user: User; rememberMe: boolean } };

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        rememberMe: action.payload.rememberMe,
      };
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        rememberMe: false,
      };
    case 'RESTORE_SESSION':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        rememberMe: action.payload.rememberMe,
      };
    default:
      return state;
  }
};

// Hardcoded credentials for demo
const DEMO_CREDENTIALS = {
  username: 'admin',
  password: 'password123',
  user: {
    id: '1',
    username: 'admin',
    name: 'John Admin',
    email: '<EMAIL>',
  }
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    // Check for saved session
    const savedUser = localStorage.getItem('insuranceApp_user');
    const savedRememberMe = localStorage.getItem('insuranceApp_rememberMe');
    
    if (savedUser && savedRememberMe === 'true') {
      dispatch({
        type: 'RESTORE_SESSION',
        payload: {
          user: JSON.parse(savedUser),
          rememberMe: true,
        }
      });
    }
  }, []);

  const login = async (username: string, password: string, rememberMe: boolean): Promise<boolean> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (username === DEMO_CREDENTIALS.username && password === DEMO_CREDENTIALS.password) {
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user: DEMO_CREDENTIALS.user,
          rememberMe,
        }
      });

      if (rememberMe) {
        localStorage.setItem('insuranceApp_user', JSON.stringify(DEMO_CREDENTIALS.user));
        localStorage.setItem('insuranceApp_rememberMe', 'true');
      }

      return true;
    }

    return false;
  };

  const logout = () => {
    dispatch({ type: 'LOGOUT' });

    // Clear all authentication data
    localStorage.removeItem('insuranceApp_user');
    localStorage.removeItem('insuranceApp_rememberMe');

    // Clear all application data for fresh start
    localStorage.clear(); // This clears everything to ensure fresh start

    // Force page reload to reset all contexts and state
    window.location.reload();
  };

  const resetPassword = async (email: string): Promise<boolean> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    return true;
  };

  return (
    <AuthContext.Provider value={{
      ...state,
      login,
      logout,
      resetPassword,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};