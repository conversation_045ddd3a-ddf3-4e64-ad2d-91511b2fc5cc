// Illustration Data Service - Handles fetching selected scenario illustration data
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Types for illustration data
export interface IllustrationTableRow {
  policy_year: number;
  end_of_age: number;
  planned_premium: number;
  net_outlay: number;
  net_surrender_value: number;
  net_death_benefit: number;
}

export interface IllustrationData {
  scenario_id: number;
  policy_id: number;
  illustration_type: string;
  illustration_question: string;
  selected_options: string[];
  table_data: IllustrationTableRow[];
  created_date: string;
  updated_date: string;
}

export interface IllustrationRequest {
  policyId: string | number;
  scenarioId: string | number;
}

export interface IllustrationResponse {
  success: boolean;
  data: IllustrationData | null;
  message?: string;
}

/**
 * Fetch selected scenario illustration data from backend API
 * @param requestData - The illustration request parameters
 * @returns Promise with illustration response
 */
export const fetchSelectedScenarioIllustration = async (requestData: IllustrationRequest): Promise<IllustrationResponse> => {
  try {
    console.log('🔍 Fetching illustration data from:', `${API_BASE_URL}/get_selected_scenario`);
    console.log('📋 Request data:', requestData);

    // Convert request data to URL parameters
    const params = new URLSearchParams({
      policy_id: requestData.policyId.toString(),
      scenario_id: requestData.scenarioId.toString()
    });
    
    const url = `${API_BASE_URL}/get_selected_scenario?${params.toString()}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error Response:', errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const responseText = await response.text();
    console.log('📄 Raw response text:', responseText);

    let data;
    try {
      data = responseText ? JSON.parse(responseText) : null;
    } catch (parseError) {
      console.error('❌ Failed to parse JSON:', parseError);
      throw new Error('Invalid JSON response from server');
    }

    if (!data) {
      return {
        success: false,
        data: null,
        message: 'No data received from server'
      };
    }

    console.log('📊 Processed illustration data:', data);

    return {
      success: true,
      data: data
    };

  } catch (error) {
    console.error('❌ Error in fetchSelectedScenarioIllustration:', error);
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : 'Failed to fetch illustration data'
    };
  }
};

/**
 * Fetch illustration data with retry functionality
 * @param requestData - The illustration request parameters
 * @param retryCount - Number of retry attempts (default: 2)
 * @returns Promise with illustration response
 */
export const fetchSelectedScenarioWithRetry = async (
  requestData: IllustrationRequest, 
  retryCount: number = 2
): Promise<IllustrationResponse> => {
  let lastError: Error | null = null;
  
  for (let attempt = 0; attempt <= retryCount; attempt++) {
    try {
      console.log(`🔄 Illustration fetch attempt ${attempt + 1}/${retryCount + 1}`);
      const result = await fetchSelectedScenarioIllustration(requestData);
      
      if (result.success) {
        console.log('✅ Illustration data fetched successfully');
        return result;
      } else {
        lastError = new Error(result.message || 'Unknown error');
      }
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      console.warn(`⚠️ Illustration fetch attempt ${attempt + 1} failed:`, lastError.message);
      
      // Wait before retry (except on last attempt)
      if (attempt < retryCount) {
        await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
      }
    }
  }
  
  return {
    success: false,
    data: null,
    message: lastError?.message || 'Failed to fetch illustration data after retries'
  };
};

/**
 * Convert backend illustration data to frontend table format
 * @param illustrationData - Backend illustration data
 * @returns Frontend table data format
 */
export const convertIllustrationToTableData = (illustrationData: IllustrationData): any[] => {
  if (!illustrationData || !illustrationData.table_data) {
    console.warn('⚠️ No table data in illustration response');
    return [];
  }

  return illustrationData.table_data.map(row => ({
    policyYear: row.policy_year,
    endOfAge: row.end_of_age,
    plannedPremium: row.planned_premium,
    netOutlay: row.net_outlay,
    netSurrenderValue: row.net_surrender_value,
    netDeathBenefit: row.net_death_benefit
  }));
};

/**
 * Initialize illustration data for a selected scenario
 * @param policyId - Policy ID
 * @param scenarioId - Scenario ID
 * @param setLoadingIllustration - Loading state setter
 * @param setIllustrationError - Error state setter
 * @param setIllustrationData - Data state setter
 * @returns Promise with void
 */
export const initializeIllustrationData = async (
  policyId: string | number,
  scenarioId: string | number,
  setLoadingIllustration: (loading: boolean) => void,
  setIllustrationError: (error: string | null) => void,
  setIllustrationData: (data: any[]) => void
): Promise<void> => {
  if (!policyId || !scenarioId) {
    setIllustrationData([]);
    return;
  }

  setLoadingIllustration(true);
  setIllustrationError(null);

  try {
    console.log('🚀 Initializing illustration data for:', {
      policyId,
      scenarioId
    });

    const response = await fetchSelectedScenarioWithRetry({
      policyId,
      scenarioId
    });

    if (response.success && response.data) {
      const tableData = convertIllustrationToTableData(response.data);
      setIllustrationData(tableData);
      console.log('✅ Illustration data initialized:', tableData);
    } else {
      console.warn('⚠️ Invalid illustration data format:', response);
      throw new Error(response.message || 'Invalid illustration data format received');
    }
  } catch (error) {
    console.error('❌ Error initializing illustration data:', error);
    setIllustrationError(error instanceof Error ? error.message : 'Failed to load illustration data');
    setIllustrationData([]);
  } finally {
    setLoadingIllustration(false);
  }
};

/**
 * Get illustration data for a specific scenario
 * @param policyId - Policy ID
 * @param scenarioId - Scenario ID
 * @returns Promise with table data or empty array
 */
export const getIllustrationTableData = async (
  policyId: string | number,
  scenarioId: string | number
): Promise<any[]> => {
  try {
    const response = await fetchSelectedScenarioWithRetry({
      policyId,
      scenarioId
    });

    if (response.success && response.data) {
      return convertIllustrationToTableData(response.data);
    } else {
      console.warn('⚠️ Failed to fetch illustration data:', response.message);
      return [];
    }
  } catch (error) {
    console.error('❌ Error getting illustration table data:', error);
    return [];
  }
};
