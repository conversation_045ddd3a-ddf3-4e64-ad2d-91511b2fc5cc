from fastapi import APIRouter, HTTPException
# from app.models.store_selected_illustrations import StoreIllustrationOptionsRequest, StoreIllustrationOptionsResponse
from app.models.store_selected_illustrations import SelectedOptionsRequest, StoreSelectedOptionsResponse
from app.services.store_selected_illustrations_services import store_selected_options

selected_illustrations_routes = APIRouter()
@selected_illustrations_routes.post("/api/illustration/store_options", response_model=StoreSelectedOptionsResponse)
def store_selected_options_route(request: SelectedOptionsRequest):  # ✅ use a different name
    try:
        print(f"🔍 Route received request: {request}")
        result = store_selected_options(request)
        return result
    except Exception as e:
        print(f"❌ Route error: {str(e)}")
        print(f"❌ Route error type: {type(e)}")
        raise HTTPException(status_code=500, detail=str(e))