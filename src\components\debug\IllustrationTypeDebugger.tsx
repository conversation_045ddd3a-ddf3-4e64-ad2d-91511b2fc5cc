import React, { useState } from 'react';
import { useDashboard } from '../../contexts/DashboardContext';
import Card from '../common/Card';
import Button from '../common/Button';
import { CheckCircle, XCircle, AlertTriangle, RefreshCw } from 'lucide-react';

interface IllustrationTypeStatus {
  id: number;
  name: string;
  component: string;
  isAllowed: boolean;
  hasErrors: boolean;
  errorMessage?: string;
}

const IllustrationTypeDebugger: React.FC = () => {
  const { allowedIllustrationTypes, selectedPolicyData, selectedCustomerData, fetchAllowedIllustrationTypes } = useDashboard();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const illustrationTypes: Omit<IllustrationTypeStatus, 'isAllowed' | 'hasErrors'>[] = [
    { id: 1, name: 'AS-IS', component: 'AsIsPage' },
    { id: 2, name: 'Face Amount', component: 'FaceAmountPage' },
    { id: 3, name: 'Premium', component: 'PremiumPage' },
    { id: 4, name: 'Interest Rate', component: 'InterestRatePage' },
    { id: 5, name: 'Full Surrender / Income', component: 'IncomePage' },
    { id: 6, name: 'Loan Repayment', component: 'LoanRepaymentPage' }
  ];

  const getTypeStatus = (): IllustrationTypeStatus[] => {
    return illustrationTypes.map(type => ({
      ...type,
      isAllowed: allowedIllustrationTypes.includes(type.id),
      hasErrors: false, // We'll enhance this later with actual error checking
      errorMessage: undefined
    }));
  };

  const handleRefreshTypes = async () => {
    if (!selectedPolicyData || !selectedCustomerData) {
      alert('Please select a policy first');
      return;
    }

    setIsRefreshing(true);
    try {
      const policyId = parseInt(selectedPolicyData.id || selectedCustomerData.policyNumber || '1');
      const customerId = parseInt(selectedCustomerData.customerId || '1');
      
      await fetchAllowedIllustrationTypes(policyId, customerId, 'life-insurance');
    } catch (error) {
      console.error('Error refreshing illustration types:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const typeStatuses = getTypeStatus();
  const allowedCount = typeStatuses.filter(t => t.isAllowed).length;
  const totalCount = typeStatuses.length;

  return (
    <Card className="max-w-4xl mx-auto">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Illustration Types Debugger
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Debug which illustration types are working and which have issues
            </p>
          </div>
          <Button
            onClick={handleRefreshTypes}
            disabled={isRefreshing || !selectedPolicyData}
            className="flex items-center space-x-2"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            <span>Refresh Types</span>
          </Button>
        </div>

        {/* Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {allowedCount}/{totalCount}
            </div>
            <div className="text-sm text-blue-700 dark:text-blue-300">Types Allowed</div>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {typeStatuses.filter(t => t.isAllowed && !t.hasErrors).length}
            </div>
            <div className="text-sm text-green-700 dark:text-green-300">Working</div>
          </div>
          
          <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
            <div className="text-2xl font-bold text-red-600 dark:text-red-400">
              {typeStatuses.filter(t => !t.isAllowed || t.hasErrors).length}
            </div>
            <div className="text-sm text-red-700 dark:text-red-300">Issues</div>
          </div>
        </div>

        {/* Current Policy Info */}
        {selectedPolicyData && (
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-6">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Current Policy</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Policy ID:</span>
                <span className="ml-2 font-medium">{selectedPolicyData.id || 'N/A'}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Customer ID:</span>
                <span className="ml-2 font-medium">{selectedCustomerData?.customerId || 'N/A'}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Policy Type:</span>
                <span className="ml-2 font-medium">{selectedPolicyData.name || 'N/A'}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Allowed Types:</span>
                <span className="ml-2 font-medium">[{allowedIllustrationTypes.join(', ')}]</span>
              </div>
            </div>
          </div>
        )}

        {/* Type Status List */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Illustration Type Status
          </h3>
          
          {typeStatuses.map((type) => (
            <div
              key={type.id}
              className={`flex items-center justify-between p-4 rounded-lg border ${
                type.isAllowed && !type.hasErrors
                  ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                  : type.isAllowed && type.hasErrors
                  ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
                  : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  {type.isAllowed && !type.hasErrors ? (
                    <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                  ) : type.isAllowed && type.hasErrors ? (
                    <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                  )}
                  <span className="font-medium text-gray-900 dark:text-white">
                    Type {type.id}: {type.name}
                  </span>
                </div>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  ({type.component})
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  type.isAllowed
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-300'
                    : 'bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-300'
                }`}>
                  {type.isAllowed ? 'Allowed' : 'Blocked'}
                </span>
                
                {type.hasErrors && (
                  <span className="px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/40 dark:text-yellow-300">
                    Has Errors
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Debug Info */}
        <div className="mt-6 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Debug Information</h4>
          <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
            <div>Backend API Endpoint: <code>/illustration-options/{'{policyId}'}</code></div>
            <div>Allowed Types Array: <code>[{allowedIllustrationTypes.join(', ')}]</code></div>
            <div>Total Types Available: <code>{totalCount}</code></div>
            <div>Types Currently Allowed: <code>{allowedCount}</code></div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default IllustrationTypeDebugger;
