# Life Insurance Project

## Environment Setup

### Required Environment Variables

This project requires environment variables to be configured in a `.env` file in the project root.

#### 1. Copy the example file
```bash
cp .env.example .env
```

#### 2. Configure the API Base URL
Edit the `.env` file and set the backend API URL:

```bash
# API Configuration
VITE_API_BASE_URL=http://127.0.0.1:8000
```

**Important**: The `VITE_API_BASE_URL` environment variable is required and must be set in the `.env` file. No fallback values are used to ensure explicit configuration.

### Development

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Start the backend server (in a separate terminal):
```bash
cd backend
# Follow backend setup instructions
```

### Environment Variable Notes

- All Vite environment variables must be prefixed with `VITE_`
- Restart the development server after changing environment variables
- The `.env` file should not be committed to version control
- Use `.env.example` as a template for required variables

### Backend Configuration

The backend API should be running on the URL specified in `VITE_API_BASE_URL`. Default development URL is `http://127.0.0.1:8000`.
