from fastapi import FastAPI
from app.api_routes import Policy_View_Routes
from app.api_routes.Policy_Search_View_routes import router as policy_router
from fastapi.middleware.cors import CORSMiddleware
from app.api_routes.store_selected_illustrations_routes import selected_illustrations_routes
from app.api_routes.get_illustration_scenario_rotes import get_scenario
from app.api_routes.filter_routes import router
from app.api_routes.disclosure_routes import disclosure_router
app = FastAPI()


app.include_router(policy_router)
app.include_router(Policy_View_Routes.router,prefix="/View_Policy")
# app.include_router(router, prefix="/api")
app.include_router(selected_illustrations_routes)
app.include_router(get_scenario)
app.include_router(router)
app.include_router(disclosure_router)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # You can restrict this to ["http://localhost:3000"] etc.
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


